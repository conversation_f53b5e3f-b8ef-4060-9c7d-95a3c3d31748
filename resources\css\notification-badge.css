/* Notification Badge Styles */
/* Matches the existing Blade template styling */

.notification {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: absolute;
}

.notification-danger {
    background-color: #dc3545; /* Bootstrap danger color */
}

.sidebar-notification {
    top: 8px;
    right: 8px;
    z-index: 10;
}

/* Optional: Notification count styling (if showCount is enabled) */
.notification-count {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #dc3545;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
    line-height: 1.2;
}

/* Ensure proper positioning for menu items */
.ant-menu-item.relative {
    position: relative !important;
}

/* Animation for notification badge */
.notification {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .sidebar-notification {
        top: 6px;
        right: 6px;
        width: 6px;
        height: 6px;
    }
    
    .notification-count {
        font-size: 8px;
        padding: 1px 4px;
        min-width: 12px;
    }
}
