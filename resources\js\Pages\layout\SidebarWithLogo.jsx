import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd";
import { usePage } from "@inertiajs/react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faGlobe } from "@fortawesome/free-solid-svg-icons";
import Logo from "../../../../public/v2/images/logo.png";
import { router } from "@inertiajs/react";

// Import SVG icons
import DashboardIcon from "../../../../public/v2/icons/dashboard-icon.svg";
import AttributesetIcon from "../../../../public/v2/icons/attributeset-icon.svg";
import GalleryIcon from "../../../../public/v2/icons/gallery-icon.svg";
import InviteteamIcon from "../../../../public/v2/icons/inviteteam-icon.svg";
import PlansIcon from "../../../../public/v2/icons/plans-icon.svg";
import NotificationIcon from "../../../../public/v2/icons/notification-icon.svg";
import HelpIcon from "../../../../public/v2/icons/help-icon.svg";
import SettingsIcon from "../../../../public/v2/icons/settings-icon.svg";
import LogoutIcon from "../../../../public/v2/icons/logout-icon.svg";

const SidebarWithLogo = ({ activeKey }) => {
    const { url } = usePage();
    const [selectedKey, setSelectedKey] = useState("");
    const logoutFormRef = useRef(null);

    const menuItems = [
        {
            key: "/dashboard",
            label: "Dashboard",
            icon: <img src={DashboardIcon} alt="Dashboard" className="w-5 h-5" />,
        },
        {
            key: "/products",
            label: "Products",
            icon: <img src={AttributesetIcon} alt="Attribute Sets" className="w-5 h-5" />,
        },
        {
            key: "/gallery",
            label: "Gallery",
            icon: <img src={GalleryIcon} alt="Gallery" className="w-5 h-5" />,
        },
        {
            key: "/brandportal/create",
            label: "Brand Portal",
            icon: <FontAwesomeIcon icon={faGlobe} className="w-5 h-5" />,
        },
        {
            key: "/invite_team/index",
            label: "Invite Team",
            icon: <img src={InviteteamIcon} alt="Invite Team" className="w-5 h-5" />,
        },
        {
            key: "/billing",
            label: "Plans",
            icon: <img src={PlansIcon} alt="Plans" className="w-5 h-5" />,
        },
        {
            key: "/notification",
            label: "Notification",
            icon: <img src={NotificationIcon} alt="Notification" className="w-5 h-5" />,
        },
        {
            key: "https://support.apimio.com",
            label: "Help",
            icon: <img src={HelpIcon} alt="Help" className="w-5 h-5" />,
        },
        {
            key: "/pages/settings",
            label: "Settings",
            icon: <img src={SettingsIcon} alt="Settings" className="w-5 h-5" />,
        },
        {
            key: "logout",
            label: "Logout",
            icon: <img src={LogoutIcon} alt="Logout" className="w-5 h-5" />,
        },
    ];

    // Determine the active menu item based on the current URL
    useEffect(() => {
        // If activeKey is provided as a prop, use it
        // if (activeKey) {
        //     const formattedKey = activeKey.startsWith("/") ? activeKey : `/${activeKey}`;
        //     setSelectedKey(formattedKey);
        //     return;
        // }

        // Otherwise, detect from URL path
        const path = url.split("?")[0]; // Remove query parameters

        // Exact matching for root path
        if (path === "/" || path === "") {
            setSelectedKey("/dashboard");
            return;
        }

        // Check for specific path patterns first (priority matching)

        // Product routes - any URL containing /products should highlight the Products menu item
        if (path.includes("/products")) {
            setSelectedKey("/products/all-products");
            return;
        }

        // Invite team routes
        if (path.includes("/invite_team")) {
            setSelectedKey("/invite_team/index");
            return;
        }

        // Settings routes
        if (path.includes("/settings") || path.includes("/pages/settings")) {
            setSelectedKey("/pages/settings");
            return;
        }

        // Try exact match
        const exactMatch = menuItems.find((item) => item.key !== "logout" && (path === item.key || path === item.key + "/"));

        if (exactMatch) {
            setSelectedKey(exactMatch.key);
            return;
        }

        // Try partial match - check if the current path starts with any menu key
        const partialMatch = menuItems.find((item) => item.key !== "logout" && path.startsWith(item.key + "/"));

        if (partialMatch) {
            setSelectedKey(partialMatch.key);
            return;
        }

        // Try segment match - check if any menu key is included in the path segments
        const pathSegments = path.split("/").filter(Boolean);

        for (const item of menuItems) {
            if (item.key === "logout") continue;

            const keySegments = item.key.split("/").filter(Boolean);

            // Check if any segment from the menu key appears in the path
            for (const segment of keySegments) {
                if (segment && pathSegments.includes(segment)) {
                    setSelectedKey(item.key);
                    return;
                }
            }
        }

        // If all else fails, try to extract the top-level path
        const topLevelPath = "/" + path.split("/")[1];

        const topLevelMatch = menuItems.find((item) => item.key !== "logout" && item.key.startsWith(topLevelPath));

        if (topLevelMatch) {
            setSelectedKey(topLevelMatch.key);
        }
    }, [url, activeKey]);

    const handleMenuSelect = ({ key }) => {
        if (key === "logout") {
            // Programmatically submit the logout form
            if (logoutFormRef.current) {
                logoutFormRef.current.submit();
            }
        } else {
            // Handle help link to open in new tab
            if (key === "https://support.apimio.com") {
                window.open(key, "_blank");
                return;
            }

            // Ensure path starts with a slash for consistency
            const path = key.startsWith("/") ? key : `/${key}`;

            // Use Inertia router only for dashboard and products
            if (path === "/dashboard") {
                // Use Inertia router for dashboard and products pages
                router.visit(path, {
                    preserveScroll: false, // Don't preserve scroll position
                    preserveState: false, // Don't preserve component state
                    replace: true, // Replace the current history entry
                });
            } else {
                // Use direct browser navigation for all other routes
                window.location.href = path;
            }
        }
    };

    return (
        <div className="bg-gray-100 h-full min-h-screen flex">
            <div
                className="sidebar-container flex flex-col justify-between"
                style={{
                    width: "200px",
                    height: "100%",
                }}
            >
                {/* Logo Section */}
                <div className="bg-white p-4 flex justify-center border-b border-[#D9D9D9]">
                    <span onClick={() => router.visit("/dashboard")}>
                        <img src={Logo} alt="Logo" className="w-36 cursor-pointer" />
                    </span>
                </div>
                
                <Menu mode="inline" selectedKeys={[selectedKey]} onSelect={handleMenuSelect} className="bg-white h-full flex flex-col">
                    {/* Top Menu Items */}
                    {menuItems.map((item) => (
                        <Menu.Item
                            key={item.key}
                            className={`rounded-none !py-[14px] !px-[11px] ${
                                selectedKey === item.key
                                    ? "border-r-[3px] border-[#740898] !text-[#740898] !bg-[#F9FAFF]"
                                    : "border-r-[3px] border-transparent"
                            }`}
                            style={{ borderRadius: "0px", margin: 0, width: "100%" }}
                        >
                            <div className="flex items-center gap-[11px] p-0 m-0">
                                {item.icon}
                                {item.label}
                            </div>
                        </Menu.Item>
                    ))}
                </Menu>

                {/* Moved Manage Stores Button to bottom */}
                <div className="bg-white px-[11px] py-[14px]">
                    <Button
                        block
                        className="bg-[#2C4BFF0D] text-black !border-[#D9D9D9] !border !rounded-[4px] !h-[32px]"
                        onClick={() => (window.location.href = "/channel")}
                    >
                        <div className="flex items-center gap-2 justify-center">
                            <img src={SettingsIcon} alt="settings icon" className="w-[14px] h-[14px]" />
                            <span>Manage Stores</span>
                        </div>
                    </Button>
                </div>

                {/* Hidden Logout Form */}
                <form
                    ref={logoutFormRef}
                    action="/logout" // Replace with your actual logout URL
                    method="POST"
                    style={{ display: "none" }}
                >
                    {/* Include CSRF Token if required by your backend */}
                </form>
            </div>
        </div>
    );
};

export default SidebarWithLogo;
