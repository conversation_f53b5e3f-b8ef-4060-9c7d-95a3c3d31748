import React, { useState, useContext } from "react";
import { Layout, Card, Timeline, Button, Checkbox, Row, Col, Form, Input, Select, Modal, Image } from "antd";
import { OnboardingContext } from "./OnboardingContext";
import RightArrow from "../../../../public/v2/icons/right-arrow.svg";
import OrganizationIconOne from "../../../../public/v2/icons/organization-icon-1.svg";
import OrganizationIconTwo from "../../../../public/v2/icons/organization-icon-2.svg";
import ExcelIcon from "../../../../public/v2/icons/excel-icon.svg";
import ShopifyIcon from "../../../../public/v2/icons/shopify-icon.svg";
import BoxIcon from "../../../../public/v2/icons/box-icon.svg";
import ShopifyUrlModal from "../../components/ShopifyUrlModal";

const { Content } = Layout;

const OnboardingNine = () => {
    const { handleNext } = useContext(OnboardingContext);
    const { shopifyurl, setShopifyurl } = useContext(OnboardingContext);
    const [isModalVisible, setIsModalVisible] = useState(false);

    const options = [
        {
            id: 1,
            name: "Import Your Existing CSV ",
            img: ExcelIcon,
            fun: () => {
                window.location.href = "/products/import/step1";
            },
        },
        {
            id: 2,
            name: "Connect Your Shopify Store to Import Products",
            img: ShopifyIcon,
            fun: () => {
                setIsModalVisible(true);
            },
        },
        {
            id: 3,
            name: "Add Product Manually",
            img: BoxIcon,
            fun: () => {
                window.location.href = "/products";
            },
        },
    ];

    return (
        <Layout>
            <Content className="flex flex-col md:flex-row">
                <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] h-screen">
                    <h2 className="text-[40px] text-center font-bold pb-[10px]">Start Onboarding your products</h2>
                    <p className="text-gray-500 text-center mb-6">Yay!, You are just one more step away</p>

                    {options.map((opt) => (
                        <div
                            key={opt.id}
                            onClick={opt.fun}
                            className="max-w-4xl cursor-pointer  mx-auto mb-[20px] p-[18px] bg-[white] rounded-[12px] border border-[#D9D9D9]"
                        >
                            <div className="flex items-center">
                                <div className="w-1/2 flex items-center gap-[17px]">
                                    <Image src={opt.img} alt="organization Icon" />
                                    <p className="font-[700] text-[16px]">{opt.name}</p>
                                </div>
                                <div className="w-1/2 flex justify-end">
                                    <Image src={RightArrow} alt="right arrow" />
                                </div>
                            </div>
                        </div>
                    ))}

                    <div className="flex justify-center mt-6 gap-[30px]">
                        <Button
                            className="bg-[#740898] text-[14px] rounded-[4px] mt-[40px] font-[400] h-[32px] px-[16px] py-[4px] border border-[#740898] text-[#FFFFFF]"
                            onClick={() => {}}
                        >
                            Continue to Dashboard
                        </Button>
                    </div>
                </div>
            </Content>

            <ShopifyUrlModal 
                isVisible={isModalVisible}
                onClose={() => setIsModalVisible(false)}
                onConnect={handleNext}
                shopifyUrl={shopifyurl}
                onShopifyUrlChange={setShopifyurl}
            />
        </Layout>
    );
};
export default OnboardingNine;
