import React, { useState, useEffect, useRef } from "react";
import { Layout, Button, Form, Input, Modal, Image, message, Select, Spin } from "antd";
import ReactDOM from "react-dom/client";
import { Head } from "@inertiajs/react";
import RightArrow from "../../../../public/v2/icons/right-arrow.svg";
import OrganizationIconOne from "../../../../public/v2/icons/organization-icon-1.svg";
import OrganizationIconTwo from "../../../../public/v2/icons/organization-icon-2.svg";
import { post, get } from "../../axios";
import { DownOutlined, LoadingOutlined } from "@ant-design/icons";
import { usePage } from "@inertiajs/react";
const { Content } = Layout;
const { Option } = Select;

const OrganizationSelect = ({ onNext, title = "Select Organization" }) => {
    const logoutFormRef = useRef(null);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [organizationName, setOrganizationName] = useState("");
    const [loading, setLoading] = useState(false); // Loading state for organization creation
    const [fetchingOrgs, setFetchingOrgs] = useState(true); // Loading state for fetching organizations
    const [organizations, setOrganizations] = useState([]);
    const [isAtBottom, setIsAtBottom] = useState(false);
    const [form] = Form.useForm();
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [selectingOrg, setSelectingOrg] = useState(false); // Loading state for organization selection
    const [errors, setErrors] = useState({}); // Add errors state
    const { props } = usePage();
        const { auth } = props;
        const user = auth?.user;

    useEffect(() => {
        const fetchOrganizations = async () => {
            setFetchingOrgs(true);
            try {
                const res = await get("/organization");
                setOrganizations(res.organizations);
            } catch (error) {
                console.error("Error fetching organizations:", error);
                message.error("Failed to load organizations. Please refresh the page.");
            } finally {
                setFetchingOrgs(false);
            }
        };

        fetchOrganizations();
    }, []);

    console.log("organizations:", organizations);

    // Custom loading spinner
    const antIcon = <LoadingOutlined style={{ fontSize: 40 }} spin />;

    const checkFormValid = () => {
        const fieldsValues = form.getFieldsValue(["name", "language", "currency", "separator", "weightUnit"]);
        const hasEmpty = Object.values(fieldsValues).some((value) => value === undefined || value === null || value === "");
        const fieldsError = form.getFieldsError();
        const hasErrors = fieldsError.some(({ errors }) => errors.length > 0);
        setIsButtonDisabled(hasErrors || hasEmpty);
    };

    const handleCreateOrganization = async (values) => {
        setLoading(true);
        try {
            const response = await post("organization", values);
            message.success(`Organization "${values.name}" created successfully!`);
            setIsModalVisible(false);
            form.resetFields();
            setErrors({}); // Clear any previous errors

            // Refresh organizations list
            const res = await get("/organization");
            setOrganizations(res.organizations);

            if (onNext) {
                onNext();
            }
        } catch (error) {
            console.error("Error creating organization:", error);
            message.error(error.response?.data?.message || "Failed to create organization. Please try again.");

            // Set form errors if they come from the server
            if (error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            }
        } finally {
            setLoading(false);
        }
    };

    const demoOrganizations = [
        { id: 1, name: "Organization Example 1", img: OrganizationIconOne },
        { id: 2, name: "Organization Example 2", img: OrganizationIconTwo },
    ];

    const handleOrganizationSelect = (org) => {
        setSelectingOrg(true);
        // Navigate to organization-specific URL
        window.location.href = `/organization/active/${org.id}`;
    };

    // Add this new function to generate initials
    const getInitials = (name) => {
        return name
            .split(" ")
            .map((word) => word[0])
            .join("")
            .toUpperCase();
    };

    // Get a consistent color based on organization ID
    const getConsistentColor = (id) => {
        const colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#000", "#D4A5A5", "#9B59B6", "#3498DB"];
        // Use modulo to ensure the id maps to an available color
        return colors[id % colors.length];
    };

    const handleScroll = (e) => {
        const bottom = Math.abs(e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight) < 1;
        setIsAtBottom(bottom);
    };

    return (
        <Layout>
            <Head title={title} />
            <Content className="flex flex-col md:flex-row">
                <div className="flex-1 px-[70px] py-[40px] pb-[120px] bg-[#F8F9FA] h-screen">
                    <h2 className="text-[40px] text-center font-bold pb-[10px]">Welcome {user?.fname || ''}</h2>
                    <p className="text-gray-500 text-center mb-6">Select an organization to continue</p>

                    {/* Create New Organization Button */}
                    {/* <div className="max-w-4xl mb-[20px] mx-auto p-[18px] justify-center flex bg-white rounded-[12px] border border-dashed border-[#D9D9D9]">
                        <Button type="link" className="m-0 p-0 text-[#1890FF]" onClick={() => setIsModalVisible(true)}>
                            + Create New Organization
                        </Button>
                    </div> */}

                    <div className="relative max-w-4xl mx-auto">
                        {fetchingOrgs ? (
                            <div className="flex justify-center items-center py-20">
                                <Spin indicator={antIcon} tip="Loading organizations..." />
                            </div>
                        ) : (
                            <>
                                <div className={`${organizations.length > 7 ? "h-[500px] overflow-y-auto" : ""}`} onScroll={handleScroll}>
                                    {organizations?.map((org) => (
                                        <div
                                            onClick={() => handleOrganizationSelect(org)}
                                            key={org.id}
                                            className="mb-[20px] p-[18px] bg-white rounded-[12px] border border-[#D9D9D9] hover:bg-[#f5f5f5] cursor-pointer relative"
                                        >
                                            <div className="flex items-center">
                                                <div className="w-1/2 flex items-center gap-[17px]">
                                                    <div
                                                        className="w-[40px] h-[40px] rounded-full flex items-center justify-center text-white font-semibold"
                                                        style={{ backgroundColor: getConsistentColor(org.id) }}
                                                    >
                                                        {getInitials(org.name)}
                                                    </div>
                                                    <p className="font-semibold text-lg">{org.name}</p>
                                                </div>
                                                <div className="w-1/2 flex justify-end">
                                                    <img src={RightArrow} alt="Right Arrow" width={20} height={20} />
                                                </div>
                                            </div>
                                        </div>
                                    ))}

                                    {organizations.length === 0 && !fetchingOrgs && (
                                        <div className="text-center py-10 text-gray-500">
                                            No organizations found. Create one to get started.
                                        </div>
                                    )}
                                </div>

                                {organizations.length > 7 && !isAtBottom && (
                                    <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-center pb-2 animate-bounce">
                                        <DownOutlined className="text-gray-400 text-xl" />
                                    </div>
                                )}
                            </>
                        )}
                    </div>

                    {/* Logout Button */}
                    <div className="flex justify-center mt-6">
                        <Button
                            type="primary"
                            className="bg-[#740898] rounded-[4px] mt-[40px] font-medium h-[32px] w-[90px] border border-[#740898] text-white"
                            onClick={() => {
                                logoutFormRef.current.submit();
                            }}
                        >
                            Logout
                        </Button>
                    </div>
                </div>
            </Content>

            {/* Modal for Creating Organization */}
            <Modal
                title="Create Organization"
                open={isModalVisible}
                onCancel={() => {
                    setIsModalVisible(false);
                    form.resetFields();
                }}
                footer={null}
                destroyOnClose
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleCreateOrganization}
                    onValuesChange={checkFormValid}
                    onFieldsChange={checkFormValid}
                    initialValues={{
                        language: "en-US",
                        currency: "usd",
                        separator: ".",
                        weightUnit: "oz",
                    }}
                >
                    <Form.Item
                        label="Organization Name"
                        name="name"
                        rules={[{ required: true, message: "Please input the organization name!" }]}
                    >
                        <Input status={errors.name ? "error" : ""} placeholder="Organization Name" />
                        {errors.name && <div className="text-red-500 text-sm mt-1">{errors.name}</div>}
                    </Form.Item>

                    <Form.Item label="Language" name="language" rules={[{ required: true, message: "Please select a language!" }]}>
                        <Select status={errors.language ? "error" : ""} placeholder="Select a language">
                            <Option value="en-US">English US</Option>
                            <Option value="en-UK">English UK</Option>
                            <Option value="fr-FR">French</Option>
                        </Select>
                        {errors.language && <div className="text-red-500 text-sm mt-1">{errors.language}</div>}
                    </Form.Item>

                    <Form.Item label="Currency" name="currency" rules={[{ required: true, message: "Please select a currency!" }]}>
                        <Select status={errors.currency ? "error" : ""} placeholder="Select a currency">
                            <Option value="usd">American Dollar</Option>
                            <Option value="eur">Euro</Option>
                            <Option value="gbp">British Pound</Option>
                        </Select>
                        {errors.currency && <div className="text-red-500 text-sm mt-1">{errors.currency}</div>}
                    </Form.Item>

                    <Form.Item
                        label="Currency Decimal Separator"
                        name="separator"
                        rules={[{ required: true, message: "Please select a decimal separator!" }]}
                    >
                        <Select status={errors.separator ? "error" : ""} placeholder="Select decimal separator">
                            <Option value=".">Point</Option>
                            <Option value=",">Comma</Option>
                        </Select>
                        {errors.separator && <div className="text-red-500 text-sm mt-1">{errors.separator}</div>}
                    </Form.Item>

                    <Form.Item label="Weight Unit" name="weightUnit" rules={[{ required: true, message: "Please select a weight unit!" }]}>
                        <Select status={errors.weightUnit ? "error" : ""} placeholder="Select weight unit">
                            <Option value="oz">Oz</Option>
                            <Option value="lb">Pound</Option>
                            <Option value="kg">Kilogram</Option>
                        </Select>
                        {errors.weightUnit && <div className="text-red-500 text-sm mt-1">{errors.weightUnit}</div>}
                    </Form.Item>

                    <div className="flex justify-end gap-4">
                        <Button
                            onClick={() => {
                                setIsModalVisible(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" htmlType="submit" loading={loading} disabled={isButtonDisabled}>
                            Create
                        </Button>
                    </div>
                </Form>
            </Modal>
            {/* Hidden Logout Form */}
            <form
                ref={logoutFormRef}
                action="/logout" // Replace with your actual logout URL
                method="POST"
                style={{ display: "none" }}
            >
                {/* Include CSRF Token if required by your backend */}
                {/* Example for CSRF Token */}
                {/* <input type="hidden" name="_csrf" value={csrfToken} /> */}
            </form>
        </Layout>
    );
};

export default OrganizationSelect;
