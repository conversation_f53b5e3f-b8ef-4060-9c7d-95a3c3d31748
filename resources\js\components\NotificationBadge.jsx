import React from 'react';
import { useNotifications } from '../hooks/useNotifications';

/**
 * NotificationBadge Component
 * 
 * Displays a red notification dot when there are unread notifications
 * Uses the same CSS classes as the Blade template for consistency
 * 
 * @param {Object} props
 * @param {string} props.className - Additional CSS classes to apply
 * @param {boolean} props.showCount - Whether to show the count number (default: false, shows only dot)
 * @param {string} props.position - Position classes for the badge (default: 'absolute')
 */
const NotificationBadge = ({ 
    className = '', 
    showCount = false, 
    position = 'absolute' 
}) => {
    const { hasUnreadNotifications, unreadCount, loading } = useNotifications();

    // Don't render anything if loading or no unread notifications
    if (loading || !hasUnreadNotifications) {
        return null;
    }

    return (
        <span 
            className={`notification notification-danger me-2 sidebar-notification ${position} ${className}`}
            title={`${unreadCount} unread notification${unreadCount !== 1 ? 's' : ''}`}
        >
            {showCount && unreadCount > 0 && (
                <span className="notification-count">
                    {unreadCount > 99 ? '99+' : unreadCount}
                </span>
            )}
        </span>
    );
};

export default NotificationBadge;
