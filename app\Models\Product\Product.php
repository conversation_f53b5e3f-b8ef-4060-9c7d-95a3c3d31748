<?php

namespace App\Models\Product;


use App\Events\Product\CalculateScore;
use Event;
use Exception;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Classes\ImportExport;
use App\Models\Invite\Invite;
use App\Traits\Product\Getter;
use Apimio\Gallery\Models\File;
use App\Models\Channel\Channel;
use App\Rules\UniqueManyToMany;
use Illuminate\Validation\Rule;
use App\Models\Organization\Plan;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Events\Product\ManageBrand;
use App\Events\Product\ManageFiles;
use Illuminate\Support\Facades\Log;
use App\Exceptions\VariantException;
use App\Traits\Product\ProductTrait;
use Illuminate\Support\Facades\Auth;
use App\Events\Product\ManageInvites;
use App\Events\Product\ManageVersion;
use App\Models\ProductVariantSetting;
use App\Events\Product\ManageCategory;
use App\Events\Product\ManageChannels;
use App\Events\Product\ManageVariants;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVariant;
use App\Events\Product\ManageSeoFields;
use App\Models\Channel\ChannelLocation;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use App\Events\ChannelUpdateStatusEvent;
use App\Events\Product\ManageAttributes;
use App\Models\Organization\Organization;
use App\Providers\ProductServiceProvider;
use App\Traits\Version\VersionScoreTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use App\Models\Channel\ChannelFileProduct;
use App\Models\Billing\ShopifySubscription;

use Apimio\Gallery\Traits\ImageQualityTrait;
use App\Models\Channel\ChannelProductStatus;
use phpDocumentor\Reflection\Types\Iterable_;
use App\Events\Product\ManageFamilyAttributes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;


class Product extends Model
{
    private $data = array();
    private $stripe, $seo_title, $seo_description, $seo_url;
    use ImageQualityTrait, ProductTrait, VersionScoreTrait, Getter;

    protected $fillable = ['sku', 'organization_id', 'status', 'updated_at'];
    protected $guarded = [];
    private array $variant_ids = [];

    public static function boot()
    {
        parent::boot();

        static::addGlobalScope('organization_id', function (Builder $builder) {
            if (Auth::check()) {
                $builder->where('organization_id', '=', Auth::user()->organization_id);
            } elseif (isset($builder->getModel()->data["organization_id"])) {
                $builder->where('organization_id', '=', $builder->getModel()->data["organization_id"]);
            }
        });

        static::creating(function ($model) {
            if (!isset($model->organization_id)) {
                if (Auth::check()) {
                    $model->organization_id = Auth::user()->organization_id;
                }
            }
        });

        static::saving(function ($model) {
            if ($model->isDirty('sku') || !$model->exists) {
                $organizationId = $model->organization_id ?? Auth::user()->organization_id ?? null;
                if ($organizationId) {
                    $model->sku = $model->generateUniqueSlug($model->sku, $organizationId);
                } else {
                    log::error('Organization ID not found for product slug generation');
                }
            }
        });
    }


    /**
     * Creates a default variant with a specified version for a given product.
     *
     * @param Product $product The product for which the default variant is created.
     * @param Version|null $version The version to be assigned to the default variant. If null, the default version will be used.
     * @return Product The updated product with the newly created default variant.
     */
    public static function create_default_variant_with_version($product, $version = null)
    {
        $defaultVariant = new Variant();
        $defaultVariant->setInventoryCreatedTokenWithOrganization(true, $product->organization_id ?? null);
        $defaultVariant->name = 'Title';
        $defaultVariant->version_id = $version ? $version->id : Version::where('is_default', 1)->first()->id;
        $attribute = Attribute::where('name', 'Title')
            ->where('attribute_type_id', 13)
            ->where('is_default', 1)
            ->first();

        if ($attribute) {
            $options = $attribute->attribute_options()->pluck('name')->toArray();
            $defaultVariant->option = json_encode([
                "attributes" => [
                    [
                        "id" => strval($attribute->id)
                    ]
                ],
                "options" => $options,
            ]);
        } else {
            try {
                $productId = $product->id ?? 'unknown';
                $organizationId = $product->organization_id ?? 'unknown';
                throw new \Exception("Default variant creation failed for Product ID: $productId, Organization ID: $organizationId - Required attribute not found.");
            } catch (\Exception $e) {
                Log::error($e->getMessage());
                return $product;
            }
        }
        // Save the default variant
        $product->variants()->save($defaultVariant);

        return $product;
    }

    public function generateUniqueSlug($title, $organizationId)
    {
        $slug = Str::slug($title, '-');
        $originalSlug = $slug;
        $counter = 1;
        while (Product::where('sku', $slug)->where('organization_id', $organizationId)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        return $slug;
    }

    public function scopeAllOrganizations($query)
    {
        $organizations = Organization::pluck('id')->toArray();
        return $query->whereIn('organization_id', $organizations);
    }

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        // $this->stripe = new \Stripe\StripeClient(env('STRIPE_SECRET'));
    }

    public function set_id($id)
    {
        $this->data["id"] = $id;
        return $this;
    }

    public function set_data($data)
    {
        $this->data = $data;
        return $this;
    }

    public function set_organization_id($organization_id)
    {
        $this->data['organization_id'] = $organization_id;
        return $this;
    }

    public function get_data()
    {
        return $this->data;
    }

    public function rules()
    {
        $attributes = [];

        if (isset($this->data["organization_id"])) {
            $attributes["organization_id"] = $this->data["organization_id"];
        }

        if (isset($this->data["id"])) {
            $attributes["id"] = $this->data["id"];
        }

        $editRules = array();

        if (isset($this->data["id"])) {
            $editRules = ["version_id" => "required"];
        }

        return array_merge([
            // "sku" => ["required",'max:255', new UniqueManyToMany(new Product(), $attributes)],
            "organization_id" => [Auth::check() ? "" : "required"]
        ], $editRules);
    }

    /**
     * Product model validator.
     *
     * @return \Illuminate\Validation\Validator
     */
    public function validation(): \Illuminate\Validation\Validator
    {
        return Validator::make($this->data, $this->rules());
    }

    public function attribute_rules()
    {
        return [
            "version_id" => "required",
            "product_id" => "required",
            // "channels" => "required"
        ];
    }

    public function attribute_validation()
    {
        $validator = Validator::make($this->data, $this->attribute_rules());
        return $validator;
    }

    //For variants version-wise in general tab
    public function getVariantsByVersion($productId, $versionId)
    {
        return Variant::where('product_id', $productId)
            ->where('version_id', $versionId)
            ->get();
    }


    /**
     * Store or update product using array.
     *
     * @param callback $errorCallback returns errors
     * @param callback $successCallback returns product object
     * @param bool $detaching detach relationships or not
     * @return  callable Returns $success_callback with product object when there are no error,
     * and $error_callback with errors when there are errors.
     * @deprecated $detaching param is deprecated and no longer required.
     *
     * @deprecated: This method is deprecated. Please it will only be used through Controller.
     */
    public function store(callable $errorCallback, callable $successCallback, bool $detaching = true)
    {

        // Validate product data.
        $validator = $this->validation();

        if ($validator->fails()) {
            return $errorCallback($validator->errors());
        }

        if (isset($this->data["id"])) {

            if (isset($this->data["organization_id"])) {
                $obj = $this
                    ->query()
                    ->withoutGlobalScopes()
                    ->where("organization_id", $this->data["organization_id"])
                    ->findOrFail($this->data["id"]);
            } else {
                $obj = $this->query()->findOrFail($this->data["id"]);
            }
        } else {
            $obj = $this;
        }
        if (isset($this->data["sku"])) {
            $obj->sku = $this->data["sku"]; // insertion already in dev manually 08/11/2023
        }
        if (isset($this->data["organization_id"]) && $obj->organization_id == null) {
            $obj->organization_id = $this->data["organization_id"];
        }
        if (isset($this->data["status"])) {
            $obj->status = $this->data["status"];
        } else {
            $obj->status = 0;
        }
        //       TODO: (Mubashir) need to remove this and add in their own method
        if (!isset($this->data['not_change_update_date'])) {
            $obj->updated_at = now();
        }
        $obj->save();

        /** @var Event $eventsArray */
        $eventsArray = [];
        $eventsArray[] = new ManageBrand($obj, $this->data['brand'] ?? [], true);
        $eventsArray[] = new ManageCategory($obj, $this->data["category"] ?? [], true);
        $eventsArray[] = new ManageInvites($obj, isset($this->data["vendor"]) ? $this->data["vendor"] : [],true);
        $eventsArray[] = new ManageChannels($obj, isset($this->data["channels"]) ? $this->data["channels"] : [], true);
        $eventsArray[] = new ManageFiles($obj, $this->data["files"] ?? [], null, false);
        $eventsArray[] = new ManageVersion(
            $obj,
            (isset($this->data['version_id']) ? array(['id' => $this->data['version_id']]) : [])
        );
        if (!isset($this->data['version_id'])) {
            $this->data['version_id'] = Version::query()->first()->id;
        }
        if (isset($this->data["version_id"])) {
            if (isset($this->data["attribute"])) {
                $eventsArray[] = new ManageAttributes($obj, $this->data, $this->data["version_id"]);
            }
            $eventsArray[] = new ManageSeoFields($obj, $this->data['version_id']);
        }

        if (!isset($this->data["attribute"])) {
            $this->set_name_for_product($obj, $this->data["sku"]);
        }

        $eventsArray[] = new ChannelUpdateStatusEvent(
            product: $obj,
            isUpdated: true
        );

        ProductServiceProvider::trigger($eventsArray);

        if (isset($this->data["channels"])) {
            foreach ($this->data["channels"] as $key => $channel_id) {
                $this->UpdateOrCreateInventory($channel_id, $obj);
                $channel_product = ChannelProduct::where([
                    'channel_id' => $channel_id,
                    'product_id' => $obj->id
                ])->first();

                if ($channel_product) {
                    $product_status = ChannelProductStatus::firstOrCreate(
                        [
                            'channel_product_id' => $channel_product->id,
                            'organization_id' => $obj->organization_id,
                            'type' => 'shopify'
                        ],
                        [
                            'created_at' => now(),
                            'updated_at' => now(),
                            'status' => 0,
                        ]
                    );

                    if ($product_status) {
                        $product_status->status = 0;
                        $product_status->save();
                    }
                }
            }
        }
        if (isset($this->data['variants'])) {
            $variant = json_decode($this->data['variants']);
            if (is_array($variant)) {
                $variant = reset($variant);
                $this->store_default_variant($variant);
            }
        }

        // Call events from packages.
        event(new CalculateScore($obj, isset($this->data['version_id']) ? [[$this->data['version_id']]] : []));
        return $successCallback($obj);
    }


    public function UpdateOrCreateInventory($ids = null, $product = null)
    {
        if (!is_iterable($ids)) {
            $ids = [$ids];
        }

        foreach ($ids as $id) {
            if ($id && $product) {
                foreach ($product->variants as $variant) {
                    $variant_id = $variant->id;
                    $channelLocations = ChannelLocation::where('channel_id', $id)->get();
                    foreach ($channelLocations as $location) {
                        $existingInventory = Inventory::where([
                            'organization_id' => $product->organization_id,
                            'location_id' => $location->id,
                            'variant_id' => $variant_id
                        ])->first();
                        if (!$existingInventory) {
                            Inventory::create([
                                'product_id' => $product->id,
                                'organization_id' => $product->organization_id,
                                'location_id' => $location->id,
                                'variant_id' => $variant_id,
                            ]);
                        }
                    }
                }
            }
        }
    }

    public function store_default_variant($obj): void
    {
        if ($obj) {
            if (isset($obj->id)) {
                $variant = Variant::find($obj->id);
                if (!isset($variant)) {
                    $variant = $this;
                }
            } else {
                $variant = $this;
            }

            $variant->sku = $obj->sku ?: null;
            $variant->product_id = $this->data['id'] ?: null;
            $variant->name = $obj->name ?: null;
            if (isset($this->data['quantity'])) {
                $variant->quantity = $this->data['quantity'] ?: null;
            } else {
                $variant->quantity = null;
            }
            $variant->price = $obj->price ?: null;
            $variant->version_id = $obj->version_id ?: null;
            $variant->cost_price = $obj->cost_price ?: null;
            $variant->compare_at_price = $obj->compare_at_price ?: null;
            $variant->weight = $obj->weight ?: null;
            $variant->weight_unit = $obj->weight_unit ?: null;
            $variant->barcode = $obj->barcode ?: null;
            $variant->option = $obj->option;
            $variant->save();
        }
    }

    public function set_name_for_product($product, $name)
    {
        $product->attach_default_version();
        // $firstVersion = ($product->versions()->first() !== null  ? $product->versions()->first() : Version::query()->first() );
        $firstVersion = $product->versions()->first();
        if ($firstVersion) {
            $productName = AttributeFamilyProductVersion::where("product_id", $product->id)
                ->where("version_id", $firstVersion->id)
                ->where("product_version_id", $firstVersion->pivotId)
                ->where("attribute_id", Attribute::where("handle", "product_name")->pluck('id')->first())
                ->first(); // Retrieve the first matching record
            if ($productName) {
                $productName->update(["value" => (isset($name) ? $name : $product->sku)]);
            } else {
                $attribute = Attribute::where("handle", "product_name")->first();
                $attribute_family = null;
                if ($attribute) {
                    $attribute_family = $attribute->attribute_families->first();
                }
                // Create a new record
                AttributeFamilyProductVersion::create([
                    "product_id" => $product->id,
                    "version_id" => $firstVersion->id,
                    "product_version_id" => $firstVersion->pivotId,
                    "attribute_id" => (isset($attribute_family) ? $attribute_family->attribute_id : $attribute->id),
                    'family_id' => (isset($attribute_family) ? $attribute_family->family_id : Family::where("is_default", 1)->where("name", "General")->pluck("id")->first()),
                    'attribute_family_id' => (isset($attribute_family) ? $attribute_family->id : null),
                    "value" => (isset($name) ? $name : $product->sku), // TODO: Make it a title again
                ]);
            }
        }
    }

    /**
     * @return void
     */
    public function attach_default_channel(): void
    {
        /*Saving product channel relation on product creation*/
        $default_channel = Channel::where('name', 'Default')->where('organization_id', $this->organization_id)->first();

        if ($default_channel) {
            $this->channels()->sync($default_channel->id);
        }
    }

    public function attach_default_version(): void
    {
        /*Saving product version on product creation*/
        $version_id = Version::where('is_default', 1)->where('organization_id', $this->organization_id)->value('id');
        if ($version_id) {
            if (!$this->versions()->first()) {
                $this->versions()->attach($version_id);
            }
        }
    }

    public function saveAttributeVersionValue($version, $key, $value, $update, $unit = null)
    {
        if ($update == 'update_available') {
            $attribute_family_product_version = AttributeFamilyProductVersion::where('product_version_id', $version->pivotId)
                ->where('attribute_family_id', $key)
                ->first();
            if (!$attribute_family_product_version) {
                $attribute_family_product_version = new AttributeFamilyProductVersion();
            }
        } else {
            $attribute_family_product_version = new AttributeFamilyProductVersion();
        }
        $attribute_family_product_version->attribute_family_id = $key;
        $attribute_family_product_version->value = !is_array($value) ? substr($value, -63000) : $value['value'];
        $attribute_family_product_version->product_version_id = $version->pivotId;

        if (isset($value['measurement'])) {
            $attribute_family_product_version->unit = $value['measurement'];
        } else {
            if ($unit) {
                $attribute_family_product_version->unit = $unit;
            }
        }
        // save product, version, family, and attribute ids in new column for
        // speed issues.
        $attribute_family = AttributeFamily::find($attribute_family_product_version->attribute_family_id);

        $product_version = ProductVersion::find($attribute_family_product_version->product_version_id);

        $attribute_family_product_version->attribute_id = $attribute_family->attribute_id;
        $attribute_family_product_version->family_id = $attribute_family->family_id;
        $attribute_family_product_version->product_id = $product_version->product_id;
        $attribute_family_product_version->version_id = $product_version->version_id;
        $attribute_family_product_version->save();
        $delete_with_keys[] = $key;
    }

    public function attach_variants($variants, $organization_id = null)
    {
        if (!empty($organization_id)) {
            $this->data['organization_id'] = $organization_id;
        }
        if (isset($variants["attributes"])) {
            foreach ($variants["data"] as $variant) {
                $this->saveVariant(array_merge($variant, [
                    'attributes' => $variants["attributes"]
                ]));
            }
            $this->deleteVariantsByIds();
        }
    }


    public function attach_shopify_variants($variants, $organization_id = null, $channel_id = null)
    {

        if (!empty($organization_id)) {
            $this->data['organization_id'] = $organization_id;
        }

        // Variant::where("product_id", $this->id)->delete();

        foreach ($variants as $variant) {
            $variant_data = [
                'type' => 'shopify',
                'channel_id' => $channel_id
            ];
            $this->saveVariant($variant, $variant_data);
        }
        $this->deleteVariantsByIds();
    }

    /**
     * @return void
     */
    function deleteVariantsByIds($version_ids): void
    {
        if (!empty($this->variant_ids)) {
            /* Variant::query()
                ->whereNotIn('id', $this->variant_ids)
                ->whereIn('version_id', $version_ids)
                ->where('product_id', $this->id)
                ->delete();*/

            $variants = Variant::query()
                ->where('product_id', $this->id)
                ->get();

            foreach ($variants as $variant) {
                if (!in_array($variant->id, $this->variant_ids) && in_array($variant->version_id, $version_ids)) {
                    Log::info('Shopify Variants are deleting from here - variant id = '.$variant->id);
                    Inventory::where('variant_id', $variant->id)->delete();
                    $variant->delete();
                }
            }
        }
    }

    public function saveVariantsWithVersion($variants, $data)
    {
        try {
            //add default attr when no attribute and option array send
            $variants_attributes = $variants['attributes'] ?? [];
            $variants = $variants['data'] ?? [];
            foreach ($variants as $variant) {

                //TODO: (Nadir) Will convert this code to gallery package function
                if (isset($variant['files']) && !empty($variant['files'])) {
                    foreach ($variant['files'] as $file) {
                        $image1 = File::withoutGlobalScopes()
                            ->where("organization_id", $this->organization_id ?? $data['organization_id'] ?? null)
                            ->where("link", $file["link"])
                            ->first() ?? new File();

                        if (isset($file["link"])) {
                            $image1->link = $file["link"] ?? null;
                            $image1->organization_id = $this->organization_id ?? $data['organization_id'] ?? null;
                            $image1->width = $file['width'] ?? null;
                            $image1->height = $file['height'] ?? null;
                            $image1->size = $file['size'] ?? null;
                            $image1->ext = $file['ext'] ?? null;
                            /* This condition is commented as it's not working for shopify for now */
                            // if (isset($data['method_template_type']) && in_array($data['method_template_type'], ['import', 'shopify'])) {
                            $image1->should_sync = 1;
                            // } else {
                            //     $image1->should_sync = 0;
                            // }
                            $image1->type = 'img';
                            $image1->save();
                            $variant["file_id"] = $image1->id ?? null;
                            $variant["shopify_image"] = $file['id'] ?? null;
                        }
                    }
                }
                $version_ids = [];
                if (isset($data['versions'])) {
                    foreach ($data['versions'] as $version) {
                        //if options are not attach with variant array and only default variant is coming
                        if (isset($data['options'])) {
                            $variant["options"] = $data['options'];
                        }

                        if (!isset($variant["options"]) && !isset($variant["sku"])) {
                            info('options and sku not found in variant  and variant is ' . $variant['name'] ?? 'default');
                            continue;
                        }

                        if (isset($variant["options"]) && $this->id) {
                            $variant_obj = Variant::query()
                                ->whereJsonContains('option->options', $variant["options"])
                                ->where('version_id', $version['id']);
                            if ($this->id) {
                                $variant_obj = $variant_obj->where('product_id', $this->id);
                            }
                            $variant_obj = $variant_obj->first();
                            if (!$variant_obj && !$this->id) {
                                info('SKU is not exist in during update record of ( ' . $variant['sku'] ?? null . ' )');
                                // TODO: (Nadir) need to add exception here for csv error file
                                //                            throw new VariantException();
                                continue;
                            }
                        } elseif (isset($variant['sku']) && $this->id) {
                            $variant_obj = Variant::query()
                                ->where('sku', $variant["sku"])
                                ->where('product_id', $this->id)
                                ->where('version_id', $version['id'])
                                ->first();
                            if (!$variant_obj) {
                                info('options not found in variant name ' . $variant['name'] ?? 'default');
                                continue;
                            }
                        } elseif (isset($variant["sku"])) {
                            $variant_obj = Variant::query()
                                ->where('sku', $variant["sku"])
                                ->where('version_id', $version['id'])
                                ->first();
                            if (!$variant_obj) {
                                info('options and sku not found in variant  and variant is ' . $variant['name'] ?? 'default');
                                // TODO: (Nadir) need to add exception here for csv error file
                                //                            throw new VariantException();
                                continue;
                            }
                        }
                        $version_ids[] = $version['id'];

                        if (!isset($variant_obj)) {
                            $variant_obj = new Variant();
                            $variant_obj->setInventoryCreatedTokenWithOrganization(true, $this->organization_id ?? $data['organization_id'] ?? null);
                        }
                        /*else {
                            info('variant options already exist in product_id ' . $variant_obj->product_id);
                        }*/
                        $product_id = $variant_obj->product_id ?? (isset($this->id) ? $this->id : null);
                        $variant_obj->product_id = $product_id;
                        $variant_obj->version_id = $version['id'] ?? $variant_obj->version_id;
                        $variant_obj->file_id = $variant["file_id"] ?? $variant_obj->file_id;
                        $variant_obj->option = $variant_obj->option ?? json_encode(["attributes" => $variants_attributes, "options" => $variant["options"]]);
                        $variant_obj->name = $variant["name"] ?? $variant_obj->name;
                        $variant_obj->sku = $variant["sku"] ?? $variant_obj->sku;
                        $variant_obj->weight = $variant["weight"] ?? $variant_obj->weight;
                        $variant_obj->weight_unit = $variant["weight_unit"] ?? $variant_obj->weight_unit;
                        $variant_obj->price = $variant["price"] ?? $variant_obj->price;
                        $variant_obj->compare_at_price = $variant["compare_at_price"] ?? $variant_obj->compare_at_price;
                        $variant_obj->cost_price = $variant["cost_price"] ?? $variant_obj->cost_price;
                        $variant_obj->quantity = $variant["quantity"] ?? $variant_obj->quantity;
                        $variant_obj->barcode = $variant["barcode"] ?? $variant_obj->barcode;
                        $variant_obj->save();

                        if (isset($variant["track_quantity"]) && isset($variant["continue_selling"])) {
                            $array = [];
                            $array["continue_selling"] = $variant["continue_selling"] == 1 ? 1 : 0;
                            $array["track_quantity"] = $variant["track_quantity"] == 1 ? 1 : 0;
                            if ($variant_obj->settings()->first() !== null) {
                                $variant_obj->settings()->update($array);
                            } else {
                                $variant_obj->settings()->create($array);
                            }
                        }

                        if (!$this->id) {
                            $productObj = Product::find($variant_obj->product_id);
                            event(new ChannelUpdateStatusEvent(
                                product: $productObj,
                                isUpdated: true
                            ));
                        }

                        $this->variant_ids[] = $variant_obj->id;

                        //save channel(shopify) variant
                        if (isset($variant["id"]) && isset($data['channel_id']) && !is_array($data['channel_id'])) {
                            $channel_variant = ChannelVariant::query()
                                ->where('variant_id', $variant_obj->id)
                                ->where('channel_id', $data['channel_id'])
                                ->first();
                            if (!$channel_variant) {
                                $channel_variant = new ChannelVariant();
                            }
                            $channel_variant->variant_id = $variant_obj->id;
                            $channel_variant->channel_id = $data['channel_id'];
                            $channel_variant->store_connect_id = $variant["id"];
                            $channel_variant->store_connect_image_id = $variant["shopify_image"] ?? null;
                            $channel_variant->store_connect_type = 'shopify';
                            $channel_variant->save();
                        }

                        if (isset($variant['inventory_id'])) {
                            $inventory = Inventory::query()
                                ->where('variant_id', $variant_obj->id)
                                ->whereRelation("channelLocation", "channel_id", $data['channel_id'])
                                ->first();
                            if (!$inventory) {
                                $inventory = new Inventory();
                            }
                            $inventory->variant_id = $variant_obj->id;
                            $inventory->store_connect_id = $variant['inventory_id'];
                            $inventory->product_id = $this->id ?? $variant_obj->product_id;
                            $inventory->organization_id = $this->organization_id ?? $data['organization_id'] ?? null;
                            $inventory->store_type = 'shopify';
                            $inventory->save();
                        }

                        //for inventories with multi-location
                        if (isset($variant['inventories'])) {
                            if (isset($variant['inventories']['locations'])) {
                                $locations = $variant['inventories']['locations'] ?? [];
                            } else {
                                $locations = [];
                            }

                            foreach ($locations as $loc_key => $location_qty) {
                                info('variant inventory product');
                                $channelLocations = ChannelLocation::where('location_id', $loc_key)->get();
                                if (!$channelLocations) {
                                    info('channel location not found in update record of SKU ( ' . $variant['sku'] ?? 'default' . ' )');
                                    continue;
                                }
                                foreach ($channelLocations as $location) {
                                    //dd($product->organization_id, $location->id, $variant_id);
                                    $existingInventory = Inventory::where([
                                        'organization_id' => $data['organization_id'] ?? null,
                                        'location_id' => $location->id ?? null,
                                        'variant_id' => $variant_obj->id
                                    ])->first();
                                    if (!$existingInventory) {
                                        $existingInventory = new Inventory();
                                        $existingInventory->organization_id = $data['organization_id'] ?? null;
                                        $existingInventory->location_id = $location->id ?? null;
                                        $existingInventory->variant_id = $variant_obj->id;
                                        $existingInventory->product_id = $this->id ?? $variant_obj->product_id;
                                    }

                                    $existingInventory->store_type = 'shopify';
                                    $existingInventory->available_quantity = $location_qty;
                                    $existingInventory->save();
                                }
                            }
                        }
                    }
                }
            }

            //delete variants which are not in the list
            $this->deleteVariantsByIds($version_ids);
        } catch (Exception $e) {
            Log::error("Error from variants saving: " . $e->getMessage());
        }
    }


    private function saveVariant($variant, $variant_required_data = null)
    {

        // Todo: (Nadir)we need to remove below file saving code and add to files model in proper way
        // save variant file

        if (!empty($variant['files'])) {
            foreach ($variant['files'] as $file) {
                if (isset($this->data['organization_id']["check_from"])) {
                    $file_exist = File::withoutGlobalScopes()
                        ->where("organization_id", $this->data['organization_id']["check_from"])
                        ->where("link", $file["link"])->get()->first();
                } else {
                    $file_exist = File::where("link", $file["link"])->get()->first();
                }
                if ($file_exist) {
                    $image1 = $file_exist;
                } else {
                    $image1 = new File();
                }
                $imageName = basename(parse_url($file["link"], PHP_URL_PATH));
                $image1->link = $file["link"] ?? null;
                $image1->name = $imageName ?? null;
                if (isset($this->data['organization_id']["save_to"])) {
                    $image1->organization_id = $this->data['organization_id']["save_to"];
                }
                $image1->width = $file['width'] ?? null;
                $image1->height = $file['height'] ?? null;
                $image1->size = $file['size'] ?? null;
                $image1->ext = $file['ext'] ?? null;
                $image1->should_sync = 1;
                $image1->type = 'img';
                $image1->save();
                if ($image1) {
                    $variant["file_id"] = $image1->id;
                }
            }
        }

        try {


            $variant_obj = Variant::query()->whereJsonContains('option->options', $variant["options"])->where('product_id', $this->id)->first();


            /*  $variant_sku = $variant["sku"] ?? null;
            if($variant_sku && $variant_sku != "") {
                $variant_obj = Variant::query()->where("product_id", $this->id)->where("sku", $variant_sku)->get()->first();
            }*/
            if (!$variant_obj) {
                $variant_obj = new Variant();
            }
            if (isset($variant["image_id"]) && $variant["image_id"] != "" && isset($variant["id"]) && $variant["id"] != "") {
                $variant_image = [
                    'image_id' => $variant["image_id"],
                    'variant' => $variant['id']
                ];
            }

            if (isset($variant["link"]) && $variant["link"] != "") {

                $url = strtok($variant["link"], '?');

                //convert url to image
                //                $contents = file_get_contents($url);
                //   $name = substr($url, strrpos($url, '/') + 1);

                //                //save image in s3 bucket
                //                Storage::disk('s3')->put('images/'.$name,  $contents);
                //
                //                //get image url of s3 bucket
                //                $url = Storage::disk('s3')->url('images/'.$name);


                $image1 = new File();
                $imageName = basename(parse_url($url, PHP_URL_PATH));
                $image1->link = $url ?? null;
                $image1->name = $imageName ?? null;
                if (isset($this->data['organization_id']) && isset($this->data['organization_id']["save_to"])) {
                    $image1->organization_id = $this->data['organization_id']["save_to"];
                }
                $image1->width = $variant['image_width'] ?? null;
                $image1->height = $variant['image_height'] ?? null;
                $image1->should_sync = 1;
                $image1->type = 'img';
                $image1->save();
                if ($image1) {
                    $variant["file_id"] = $image1->id;
                }
            }

            $variant_obj->product_id = $this->id;
            $variant_obj->file_id = isset($variant["file_id"]) ? $variant["file_id"] : null;
            $variant_obj->option = json_encode(["attributes" => $variant["attributes"], "options" => $variant["options"]]);
            $variant_obj->name = isset($variant["name"]) ? $variant["name"] : null;
            $variant_obj->sku = isset($variant["sku"]) ? $variant["sku"] : null;
            $variant_obj->weight = isset($variant["weight"]) ? $variant["weight"] : null;
            $variant_obj->weight_unit = isset($variant["weight"]) ? $variant["weight_unit"] : null;
            $variant_obj->price = isset($variant["price"]) ? $variant["price"] : null;
            $variant_obj->cost_price = isset($variant["cost_price"]) ? $variant["cost_price"] : null;
            $variant_obj->quantity = isset($variant["quantity"]) ? $variant["quantity"] : null;
            $variant_obj->barcode = isset($variant["barcode"]) ? $variant["barcode"] : null;
            $variant_obj->response = isset($variant_image) ? json_encode($variant_image, true) : null;
            $variant_obj->save();


            $this->variant_ids[] = $variant_obj->id;

            if (isset($variant["id"])) {

                $channel_variant = ChannelVariant::query()
                    ->where('variant_id', $variant_obj->id)
                    ->where('channel_id', $variant_required_data['channel_id'][0])
                    ->first();
                if (!$channel_variant) {
                    $channel_variant = new ChannelVariant();
                }
                $channel_variant->variant_id = $variant_obj->id;
                $channel_variant->channel_id = $variant_required_data['channel_id'][0];
                $channel_variant->store_connect_id = $variant["id"];
                $channel_variant->store_connect_image_id = $variant["image_id"] ?? null;
                $channel_variant->store_connect_type = $variant_required_data['type'] ?? 'shopify';
                $channel_variant->save();
            }

            // for import csv
            if (isset($this->data['inventory_data'])) {
                $inventory_data = $this->data['inventory_data'];
                $inventory_data['variant_id'] = $variant_obj->id;
                $inventory_data['available_quantity'] = $variant["quantity"] ?? null;
                $this->save_inventory_locations($inventory_data);
            }


            // for shopify sync
            if (isset($variant['inventory_id'])) {
                $inventory = Inventory::query()
                    ->where('variant_id', $variant_obj->id)
                    ->whereRelation("channelLocation", "channel_id", $variant_required_data['channel_id'][0])
                    ->first();
                if (!$inventory) {
                    $inventory = new Inventory();
                }
                $inventory->variant_id = $variant_obj->id;
                $inventory->store_connect_id = $variant['inventory_id'];
                $inventory->product_id = $this->id;
                $inventory->organization_id = $this->organization_id ?? $data['organization_id'] ?? null;
                $inventory->store_type = 'shopify';
                $inventory->save();
            }
        } catch (\Exception $e) {
            Log::error($e);
        }
    }

    public function save_family_data($error_callback, $success_callback)
    {
        // return $this->data;
        $validator = $this->attribute_validation();
        if ($validator->fails()) {
            return $error_callback($validator->errors());
        }

        $obj = new FamilyProductVersion();
        $obj->version_id = $this->data["version_id"];
        $obj->product_id = $this->data["product_id"];
        $obj->save();
        if (isset($this->data["attribute_family"])) {
            foreach ($this->data["attribute_family"] as $attribute_family) {
                $attribute = new AttributeFamilyProductVersion();
                $attribute->attribute_family_id = $attribute_family['id'];
                $attribute->value = $attribute_family['value'];
                $attribute->product_version_id = $obj->id;
                $attribute->save();
            }
        }
        return $success_callback($this);
    }

    public function delete_family_data($success_callback)
    {
        $family_product_version_ids = FamilyProductVersion::where('product_id', $this->data['product_id'])->where('version_id', $this->data['version_id'])->pluck('id')->toArray();
        $attribute_family_ids = AttributeFamily::where('family_id', $this->data['family_id'])->pluck('id')->toArray();

        $delete = AttributeFamilyProductVersion::whereIn('attribute_family_id', $attribute_family_ids)->whereIn('product_version_id', $family_product_version_ids)->delete();
        return $success_callback($this);
    }

    /**
     * @return void
     * @deprecated This method is deprecated.It will be directly accessed through functions of the Model.
     */
    function product_fetch_2_0($product)
    {
        if (isset($product->versions)) {
            foreach ($product->versions as $version) {


                $version->families = Family::withoutGlobalScopes()
                    ->with(["attributes", 'attributes.attribute_type', 'attributes.attribute_options'])
                    ->whereHas("values", function ($model) use ($product, $version) {
                        $model->withoutGlobalScopes()
                            ->where("product_id", $product->id)
                            ->where("version_id", $version->id);
                    })->orderBy('id')
                    ->get();

                // get values for every attribute
                foreach ($version->families as $family) {
                    foreach ($family->attributes as $attribute) {
                        $attribute->value = AttributeFamilyProductVersion::select('id', 'value', 'unit')
                            ->where("family_id", $family->id)
                            ->where("attribute_id", $attribute->id)
                            ->where("product_id", $product->id)
                            ->where("version_id", $version->id)
                            ->get();
                    }
                }
            }
        }
        return $product;
    }

    /**
     * @return void
     * @deprecated This method is deprecated.It will be directly accessed through functions of the Model.
     */
    function product_fetch($product)
    {

        if (isset($product->versions)) {
            foreach ($product->versions as $version) {

                $attribute_family_ids = AttributeFamilyProductVersion::select("attribute_family_id")
                    ->where("product_version_id", $version->pivotId)
                    ->get()->pluck('attribute_family_id')->toArray();
                $family_ids = AttributeFamily::whereIn("id", $attribute_family_ids)
                    ->pluck("family_id")
                    ->toArray();

                $family_ids = array_merge($family_ids, Family::withoutGlobalScopes()->where("organization_id", $product->organization_id)->where("is_default", 1)->pluck("id")->toArray());

                $version->families = Family::withoutGlobalScopes()
                    ->with(["attributes", 'attributes.attribute_type', 'attributes.attribute_options'])
                    ->where("organization_id", $product->organization_id)
                    ->whereIn("families.id", $family_ids)
                    ->get();

                // get values for every attribute
                foreach ($version->families as $family) {
                    foreach ($family->attributes as $attribute) {
                        $attribute->value = AttributeFamilyProductVersion::select('id', 'value', 'unit')->where("attribute_family_id", $attribute->pivotId)
                            ->where("product_version_id", $version->pivotId)->get();
                        // add unit
                        //                        $attribute->unit = AttributeFamilyProductVersion::where("attribute_family_id", $attribute->pivotId)
                        //                            ->where("product_version_id", $version->pivotId)
                        //                            ->pluck("unit")->get()->toArray();
                    }
                }
            }
        }
        return $product;
    }

    /**
     * Get the product's categories.
     * @deprecated This method is deprecated.It will be directly accessed through functions of the Model.
     */
    public function fetch($callback, $callbacks = array())
    {
        $products = Product::with([
            "brands",
            "categories",
            "inviteVendor",
            "versions",
            "channels",
            "variants",
            "files",
            "inventories"
        ]);

        if ($callback)
            $products = $callback($products);
        if (is_iterable($products)) {
            $i = 0;
            foreach ($products as $product) {
                $products[$i] = $this->product_fetch_2_0($product);
                $i++;
            }
        } else {
            $products = $this->product_fetch_2_0($products);
        }
        return $products;
    }

    public function get_families()
    {
        return Family::get();
    }

    public function get_variant_count()
    {
        $variants = $this->withCount('variants')->first();
        return $variants->variants_count;
    }

    /**
     * Get the product's categories.
     * @deprecated This method is deprecated.Instead of calling this method for total count, please directly use the product Count.
     */
    public function get_total_count()
    {
        return $this->count();
    }

    public function get_count($callback)
    {
        $products = $this->with([
            "brands",
            "categories",
            "inviteVendor",
            "versions",
            "channels",
            "product_status",
            "variants",
            "files"
        ]);

        if ($callback)
            $products = $callback($products);

        return $products;
    }

    public function get_completeness_score()
    {
        $file_obj = new File();
        return $file_obj->get_completeness_score($this->files);
    }

    /**
     * Get the product's vendors.
     * @deprecated This method is deprecated.Instead of calling this method for vendors names, please directly use the `pluck` method on the `vendors` relationship where needed.
     */
    public function get_vendors()
    {
        return implode(",", $this->vendors()->pluck("name")->toArray());
    }

    /**
     * Get the product's brands.
     * @deprecated This method is deprecated.Instead of calling this method for brand names, please directly use the `pluck` method on the `brands` relationship where needed.
     */
    public function get_brands()
    {
        return implode(",", $this->brands()->pluck("name")->toArray());
    }


    public function get_quality_stats()
    {
        $approve = 0;
        $warning = 0;
        $error = 0;

        $products = $this->fetch(function ($product) {
            return $product->limit(100)->get();
        });

        $file_obj = new File();
        foreach ($products as $product) {
            foreach ($product->files as $file) {
                $status = $file_obj->get_img_status($file);
                if ($status == "approve")
                    $approve++;
                else if ($status == "warning")
                    $warning++;
                else if ($status == "error")
                    $error++;
            }
        }

        $total = $approve + $warning + $error;

        if ($total > 0) {
            return [
                "approve" => (int)round(($approve * 100) / $total),
                "warning" => (int)round(($warning * 100) / $total),
                "error" => (int)round(($error * 100) / $total)
            ];
        }

        return [
            "approve" => $approve,
            "warning" => $warning,
            "error" => $error
        ];
    }

    public function get_status()
    {
        return $this->status ? "Active" : "Draft";
    }

    public function get_status_style()
    {
        return $this->status == 0 ? 'badge-light status-draft' : 'badge-primary status-publish';
    }

    public function scopeWhereChannelFirst($q, $channel_id)
    {
        return $q->channels->where("id", $channel_id);
    }

    /*Made by Hashir Butt (The legendary Backend developer) :D*/
    public static function count_variant()
    {
        $products = self::withCount('variants')->get();
        $total_variants = $products->sum('variants_count');

        return $total_variants;
    }

    public function plan_products($org_id)
    {
        $sub_id = DB::table('subscriptions')->where('organization_id', $org_id)->where('is_cat', null)->value('stripe_price');
        $plan_id = ShopifySubscription::where('organization_id', $org_id)->value('plan_id');
        if ($sub_id) {
            return Plan::where('stripe_monthly_id', $sub_id)->orWhere('stripe_yearly_id', $sub_id)->value('no_of_products');
        } elseif ($plan_id) {
            return Plan::where('id', $plan_id)->value('no_of_products');
        } else {
            return Plan::where('handle', 'community_plan')->value('no_of_products');
        }
    }

    /**
     * Listing product object into product array format.
     *
     * @param callback $error_callback returned with errors
     * @param callback $success_callback returned with product array
     * @param object $product product object with all relations and variants
     *
     * @return  array Returns $success_callback with product array when there are no error,
     * and $error_callback with errors when there are errors.
     */
    public function convert_apimio_product_to_array(callable $error_callback, callable $success_callback, object $product)
    {
        try {
            //sku
            $product_array['sku'] = isset($product->sku) ? $product->sku : null;

            //Product Status
            $product_array['status'] = isset($product->status) ? $product->status : null;

            //brands
            if ($product->has('brands')) {
                foreach ($product->brands as $brand)
                    $product_array['brands'][] = [
                        'name' => isset($brand->name) ? $brand->name : null,
                    ];
            }

            //            //vendors
            //            if ($product->has('inviteVendor')){
            //                foreach ($product->inviteVendor as $vendor){
            //                    $product_array['vendors'][] = [
            //                        'name' => isset($vendor->name) ? $vendor->name : null,
            //                    ];
            //                }
            //            }


            //versions
            if ($product->has('versions')) {
                foreach ($product->versions as $key => $version) {
                    $product_array['versions'][] = [
                        'name' => isset($version->name) ? $version->name : null,
                        'currency' => isset($version->currency) ? $version->currency : null
                    ];
                    if ($version->families) {
                        foreach ($version->families as $key2 => $family) {
                            $product_array['versions'][$key]['families'][] = [
                                'families' => isset($family->name) ? $family->name : null
                            ];
                            if ($family->attributes) {
                                foreach ($family->attributes as $key3 => $attribute) {
                                    $product_array['versions'][$key]['families'][$key2]['attributes'][] = [
                                        'name' => isset($attribute->name) ? $attribute->name : null
                                    ];
                                    if ($attribute->value) {
                                        foreach ($attribute->value as $value) {
                                            $product_array['versions'][$key]['families'][$key2]['attributes'][$key3]['attributes'] = [
                                                'value' => isset($value) ? $value->value : null
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //categories
            if ($product->has('categories')) {
                foreach ($product->categories as $category) {
                    if (isset($category->category_id)) {
                        $parent = Category::withoutGlobalScopes()
                            ->where("id", $category->category_id)->get()->first()->original;
                        $product_array['categories'][] = [
                            'name' => isset($parent["name"]) ? $parent["name"] : null,
                            'description' => isset($parent["description"]) ? $parent["description"] : null,
                            'category_id' => isset($parent["category_id"]) ? $parent["category_id"] : null
                        ];
                    }
                    $product_array['categories'][] = [
                        'name' => isset($category->name) ? $category->name : null,
                        'description' => isset($category->description) ? $category->description : null,
                        'category_id' => isset($category->category_id) ? $category->category_id : null
                    ];
                }
            }

            //channels
            if ($product->has('channels')) {
                foreach ($product->channels as $channel) {
                    $product_array['channels'][] = [
                        'name' => isset($channel->name) ? $channel->name : null,
                        'type' => isset($channel->type) ? $channel->type : null,
                    ];
                }
            }

            //files
            if ($product->has('files')) {
                foreach ($product->files as $file) {
                    $product_array['files'][] = [
                        'link' => isset($file->link) ? $file->link : null,
                        'width' => isset($file->width) ? $file->width : null,
                        'height' => isset($file->height) ? $file->height : null,
                        'size' => isset($file->size) ? $file->size : null,
                        'ext' => isset($file->ext) ? $file->ext : null,
                        'type' => isset($file->type) ? $file->type : null,
                        'pivot' => isset($file->pivot) ? $file->pivot->uploaded_for : null,
                    ];
                }
            }

            //families
            if ($product->has('versions')) {
                foreach ($product->versions as $version) {
                    if (isset($version->families)) {
                        foreach ($version->families as $family_key => $family) {
                            if ($family->has('attributes')) {
                                foreach ($family->attributes as $attribute) {
                                    // TODO: Moqeet we have to set this according to multi/list values.
                                    $temp_attribute = [
                                        'name' => isset($attribute->name) ? $attribute->name : null,
                                        'handle' => isset($attribute->handle) ? $attribute->handle : null,
                                        'attribute_type_id' => isset($attribute->attribute_type_id) ? $attribute->attribute_type_id : null,
                                        'value' => isset($attribute->value) ? ($attribute->value->first() ? $attribute->value->first()->value : null) : null,
                                        'unit' => isset($attribute->value) ? ($attribute->value->first() ? $attribute->value->first()->unit : null) : null,
                                    ];

                                    if ($attribute->attribute_type_id == 13) {
                                        $attribute_options = array_map(function ($item) {
                                            return ['name' => $item['name']];
                                        }, $attribute->attribute_options->toArray());
                                        $temp_attribute['attribute_options'] = isset($attribute_options) ? $attribute_options : null;
                                    }
                                    $attributes[] = $temp_attribute;
                                }
                            }
                            $product_array['families'][$family_key]['name'] = isset($family->name) ? $family->name : null;
                            $product_array['families'][$family_key]['attributes'] = !empty($attributes) ? $attributes : null;
                            unset($attributes);
                        }
                    }
                }
            }


            //variants
            if ($product->has('variants') && sizeof($product->variants) > 0) {
                foreach ($product->variants as $variant_key => $variant) {
                    $option_decode = json_decode($variant->option);
                    if (!empty($option_decode)) {
                        foreach ($option_decode->attributes as $var_attr_key => $variant_attribute) {
                            $attributes_with_values[$var_attr_key] = [
                                'name' => isset($variant_attribute->name) ? $variant_attribute->name : null,
                                'options' => !empty($variant_attribute->options) ? $variant_attribute->options : null
                            ];
                        }
                    }
                    $product_array['variants']['data'][$variant_key] = [
                        'product_id' => isset($variant->product_id) ? $variant->product_id : null,
                        'file_id' => isset($variant->file_id) ? $variant->file_id : null,
                        'options' => isset($option_decode->options) ? $option_decode->options : null,
                        'sku' => isset($variant->sku) ? $variant->sku : null,
                        "name" => isset($variant->name) ? $variant->name : null,
                        "price" => isset($variant->price) ? $variant->price : null,
                        "cost_price" => isset($variant->cost_price) ? $variant->cost_price : null,
                        "barcode" => isset($variant->barcode) ? $variant->barcode : null,
                        "quantity" => isset($variant->quantity) ? $variant->quantity : null,
                    ];
                }
                $product_array['variants']['attributes'] = isset($attributes_with_values) ? $attributes_with_values : null;
            }
        } catch (\Exception $e) {
            if ($error_callback) {
                return $error_callback($e->getMessage());
            }
        }

        if ($success_callback) {
            return $success_callback($product_array);
        }
    }


    /**
     * need to apply filtration on variants before saving
     *
     * @param array $variants
     * @return array
     * @throws Exception
     */
    public function variant_filtration_before_save(array $variants = []): array
    {
        try {
            $specifiedKeys = ["sku", "name", "price", "barcode", "cost_price", "compare_at_price", "weight", "options"];
            $seenOptions = [];
            $duplicates = [];
            $hasFilledOptions = false;
            $filteredVariants = [];

            foreach ($variants as $variant) {
                // Sort and implode options for consistent key comparison
                $options = $variant['options'] ?? [];
                sort($options);
                $optionsKey = implode(',', $options);

                // Check for filled options and mark accordingly
                if (!empty($options)) {
                    $hasFilledOptions = true;
                }

                // Identify duplicates
                if (isset($seenOptions[$optionsKey])) {
                    $duplicates[$optionsKey] = true; // Mark this options combination as a duplicate
                } else {
                    $seenOptions[$optionsKey] = true;
                }

                // Filtering logic
                $isEmptySkuAndOptions = empty($variant['sku']) && empty($options);
                $isAnyOtherKeyFilled = false;

                foreach (array_intersect_key($variant, array_flip($specifiedKeys)) as $key => $value) {
                    if ($key !== 'sku' && $key !== 'options' && !empty($value)) {
                        $isAnyOtherKeyFilled = true;
                        break;
                    }
                }

                // Check for the specific exception condition
                if ($isEmptySkuAndOptions && $isAnyOtherKeyFilled && count($variants) > 1) {
                    throw new Exception("Invalid variant: both 'sku' and 'options' are empty while another key is filled.");
                }

                if (!$isEmptySkuAndOptions || $isAnyOtherKeyFilled) {
                    $filteredVariants[] = $variant; // Add variant to the list if it passes the filter criteria
                }
            }

            // Handle duplicates
            if (!empty($duplicates)) {
                $duplicateOptions = implode(' && ', array_keys($duplicates));
                if ($duplicateOptions !== '') {
                    throw new Exception("Duplicate options found in variants: $duplicateOptions");
                }
            }

            // Ensure at least one variant has filled options
            if (!$hasFilledOptions && count($variants) > 1) {
                throw new Exception("All variants have empty option values.");
            }

            // Update product variants with the filtered list
            return $filteredVariants;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }


    /**
     * Saves given array with all the product information.
     *
     * @param callback $error_callback returned with errors
     * @param callback $success_callback returned with product object
     * @param array $products products array with information
     * @param array $organization_id optional, associative array with "save_to" to save all information in provided organization and "check_from" to query information in provided organization id
     *
     * @return  Product Returns $success_callback with product object when there are no error,
     * and $error_callback with errors when there are errors.
     */
    public function convert_to_product(callable $error_callback, callable $success_callback, array $products, Collection $non_convert_csv_product_data, array $data = [])
    {
        // validate minimum data required to save a product.
        //        $validator = Validator::make($product, [
        //            //            "handle" => "required",
        //            "families.*.name" => "required",
        //            "families.*.attributes" => "required",
        //            "families.*.attributes.*.handle" => "required",
        //            "families.*.attributes.*.attribute_type_id" => "required",
        //            "brands.*.name" => "required",
        //            "categories.*.name" => "required",
        //            "vendors.*.name" => "required",
        //            "versions.*.name" => "required_without:versions.*.id",
        //            "versions.*.id" => "required_without:versions.*.name",
        //            "channels.*.name" => "required_without:channels.*.id",
        //            "channels.*.id" => "required_without:channels.*.name",
        //            'channels.*.type' => ['required_without:channels.*.id', Rule::in(['shopify', 'magento', 'woocommerce'])],
        //            "files.*.link" => "required",
        //            // "variants.attributes"         => "required_with:variants",
        //            "variants.data" => "required_with:variants",
        //        ]);


        //        if ($validator->fails()) {
        //            dd($validator->errors());
        //            return $error_callback($validator->errors());
        //        }

        if (isset($products['products'])) {
            foreach ($products['products'] as $product) {
                $product['template_method_type'] = $products['template_method_type'] ?? null;
                DB::beginTransaction();
                try {
                    $import_action = $data['all_data']['import_action'] ?? null;

                    $product_obj = null;

                    /** @var Event $eventsArray */
                    $eventsArray = [];

                    if (isset($import_action) && in_array($import_action, ["1", "3"])) {
                        if ($product['handle']) {
                            // filtering variants before saving
                            if (isset($product['variants']) && isset($product['variants']['data'])) {
                                $product['variants']['data'] = $this->variant_filtration_before_save($product['variants']['data']);
                            }

                            // save product information
                            $sku = Str::slug($product["handle"]);
                            $product_obj = Product::query()
                                ->when(isset($data['organization_id']), function ($q) use ($data) {
                                    $q->where('organization_id', $data['organization_id']);
                                })
                                ->firstOrCreate(['sku' => $sku], [
                                    'organization_id' => $data['organization_id'] ?? null,
                                    'status' => $product["status"] ?? 0,
                                    'sku' => $sku,
                                ]);

                            /** @var Event $eventsArray */
                            $eventsArray[] = new ManageBrand($product_obj, $product['brands'] ?? [], $import_action == "1");
                            $eventsArray[] = new ManageCategory($product_obj, $product["categories"] ?? [], $import_action == "1");
                            $eventsArray[] = new ManageInvites($product_obj, $product["vendors"] ?? [], $import_action == "1");
                            $eventsArray[] = new ManageChannels($product_obj, $product["channels"] ?? [], $import_action == "1");
                            $eventsArray[] = new ManageFiles($product_obj, $product["files"] ?? []);
                            $eventsArray[] = new ManageVersion($product_obj, $product['versions'] ?? [], $import_action == "1");
                            $eventsArray[] = new ManageFamilyAttributes($product_obj, $product["families"] ?? [], $product['versions']);
                            if (isset($product['versions'])) {
                                $eventsArray[] = new ManageSeoFields($product_obj, $product['versions']);
                            }
                        }
                    }
                    // update product information if product already exists using variant sku to find product object
                    else {
                        if ($product['variant_sku']) {
                            $product_obj = Product::with('variants')
                                ->when(isset($data['organization_id']), function ($q) use ($data) {
                                    $q->where('organization_id', $data['organization_id']);
                                })
                                ->whereHas('variants', function ($q) use ($product) {
                                    $q->where('sku', $product['variant_sku']);
                                })->first();


                            if ($product_obj) {
                                /** @var Event $eventsArray */
                                $eventsArray[] = new ManageBrand($product_obj, $product['brands'] ?? [], true);
                                if (isset($product['categories'])){
                                    $eventsArray[] = new ManageCategory($product_obj, $product["categories"] ?? [], true);
                                }
                                $eventsArray[] = new ManageInvites($product_obj, $product["vendors"] ?? [], true);
                                if (isset($product['channels'])) {
                                    $eventsArray[] = new ManageChannels($product_obj, $product["channels"] ?? [], true);
                                }
                                $eventsArray[] = new ManageFiles($product_obj, $product["files"] ?? []);
                                $eventsArray[] = new ManageVersion($product_obj, $product['versions'] ?? [], true);
                                $eventsArray[] = new ManageFamilyAttributes($product_obj, $product["families"] ?? [], $product['versions']);
                                if (isset($product['versions'])) {
                                    $eventsArray[] = new ManageSeoFields($product_obj, $product['versions']);
                                }
                            }
                        }
                    }

                    $eventsArray[] = new ManageVariants(
                        $import_action != "2" ? $product_obj : null,
                        $product["variants"] ?? [],
                        [
                            'versions' => $product['versions'] ?? [],
                            'channels' => $product['channels'] ?? [],
                            'template_method_type' => $product['template_method_type'],
                            'organization_id' => $data['organization_id'] ?? null,
                        ]
                    );

                    ProductServiceProvider::trigger($eventsArray);

                    // Call events from packages.
                    if ($product_obj) {
                        event(new CalculateScore($product_obj, $product['versions'] ?? []));
                        event(new ChannelUpdateStatusEvent(
                            product: $product_obj,
                            isUpdated: true
                        ));
                    }
                    DB::commit();
                } catch (\Exception $e) {
                    Log::info(['main exception', $e->getMessage()]);
                    DB::rollBack();
                    $errorMsg = $e->getMessage();
                    $with_incorrect_rows_data = $non_convert_csv_product_data->map(function ($subCollection) use ($errorMsg) {
                        if (is_array($errorMsg)) {
                            $errorMsg = implode(',', $errorMsg);
                        }
                        return $subCollection->prepend($errorMsg, 'ERRORS/WARNINGS');
                    });

                    //////saving error row and update notification message when error occurs
                    $import_export_obj = new ImportExport();
                    $disk = Storage::disk('s3');
                    $filename = $data['filename'] ?? null;
//                    if ($filename && !$disk->exists($filename) && $with_incorrect_rows_data->isNotEmpty()) {
//                        $import_export_obj->send_notification_for_import(
//                            $data,
//                            true,
//                            "Your CSV file is now being processed by Apimio. We’ll notify you once all product data has been successfully imported"
//                        );
//                    }

                    $import_export_obj->save_error_row_in_csv($with_incorrect_rows_data, $data);
                    Log::error("import product error");
                }
            }
        }
    }


    /**
     * * Creating or updating the inventories for all selected locations
     * $inventory_data array contains [array(locations), int(product_id), int(variant_id), int(available_quantity), string(store_type), int(store_connect_id)]
     *
     * @param $inventory_data
     * @return bool
     */
    public function save_inventory_locations($inventory_data): bool
    {
        if (!isset($inventory_data['locations'])) {
            $this->create_or_update_inventory($inventory_data);
            return true;
        }

        foreach ($inventory_data['locations'] as $location) {
            $inventory_data['location_id'] = $location['id'] ?? null;
            $this->create_or_update_inventory($inventory_data);
        }

        return true;
    }

    public function create_or_update_inventory($inventory_data)
    {
        $inventory = Inventory::updateOrCreate(
            [
                'organization_id' => $inventory_data['organization_id'] ?? null,
                'location_id' => $inventory_data['location_id'] ?? null,
                'product_id' => $inventory_data['product_id'] ?? null,
                'variant_id' => $inventory_data['variant_id'] ?? null,
            ],
            [
                'available_quantity' => $inventory_data['available_quantity'] ?? null,
                //                'store_type' => $inventory_data['store_type'] ?? null,
                //                'store_connect_id' => $inventory_data['store_connect_id'] ?? null,
            ]
        );
    }

    public function save_product_status($data)
    {

        foreach ($data['cloned_product_channel_ids'] as $key => $channel_id) {
            $channel_product = ChannelProduct::where([
                'channel_id' => $channel_id,
                'product_id' => $data['cloned_product_id']
            ])->first();

            if (!$channel_product) {
                Product::query()->where('id', $data['cloned_product_id'])->first()->channel()->create([
                    'channel_id' => $channel_id,
                    'product_id' => $data['cloned_product_id']
                ]);

                $channel_product = ChannelProduct::where([
                    'channel_id' => $channel_id,
                    'product_id' => $data['cloned_product_id']
                ])->first();
            }

            $product_updated_at = now();

            $product_status = ChannelProductStatus::where([
                'organization_id' => $data['organization_id'],
                'channel_product_id' => $channel_product->id,
                'type' => $data['type']
            ])->first();
            if (!$product_status) {
                $product_status = new ChannelProductStatus();
            }
            if ($data['type'] == 'clone') {
                $product_status->response = json_encode([
                    'product_id' => (int)$data['product_id'],
                    'channel_id' => (int)$channel_id
                ]);
            } else {
                $product_status->response = json_encode([
                    'sync_id' => (int)$data['product_id']
                ]);
                $product_updated_at = Product::query()->find($data['cloned_product_id'])->updated_at ?? now();
            }

            $product_status->timestamps = false;

            $product_status->channel_product_id = $channel_product->id;
            $product_status->organization_id = $data['organization_id'];
            $product_status->type = $data['type'];
            $product_status->updated_at = $product_updated_at;
            $product_status->save();
            $product_status->timestamps = true;
        }

        return $product_status;
    }

    //getting count of filled or empty fields of product
    public function product_fields_score($product_id)
    {
        $version_ids = ProductVersion::where('product_id', $product_id)->get();
        foreach ($version_ids as $version_id) {
            $version = Version::find($version_id->version_id);
            $this->data['filled_fields'] = $version->getFilledFields($product_id);
            $this->data['empty_fields'] = $version->getEmptyFields($product_id);
        }
        return $this->data;
    }

    public function scopeSynced($query, $channel_id)
    {
        $query->whereHas('channel', function ($q) use ($channel_id) {
            $q->whereIn('channel_id', $channel_id);
            $q->whereHas('status', function ($q1) {
                $q1->where('status', '=', 1);
            });
        });
    }

    public function scopeUpdateAvailable($query, $channel_id)
    {
        $query->whereHas('channel', function ($q) use ($channel_id) {
            $q->whereIn('channel_id', $channel_id);
            $q->where(function ($q2) {
                $q2->doesnthave('status')->orWhereHas('status', function ($q1) {
                    $q1->where('status', '=', 0)->orWhereNull('updated_at');
                });
            });
        });
    }

    public function getCompletenessScoreByVersions($id)
    {
        $product = $this->find($id);

        //Total Score of product
        $score = 0;
        foreach ($product->versions()->get() as $version) {
            $score += $version->productVersionCompletnessPercentage($id);
        }
        $this->version_score = $score / (count($product->versions()->get()) == 0 ? 1 : count($product->versions()->get()));

        return $this->version_score = $this->version_score == 0 ? 8 : $this->version_score;
    }

    public function vendors()
    {
        return $this->belongsToMany(Vendor::class)->withoutGlobalScopes();
    }

    public function inviteVendor()
    {
        return $this->belongsToMany(Invite::class)->withTimestamps()->withPivot("id AS pivotId")->withoutGlobalScopes();
    }

    public function familyProductVersion()
    {
        return $this->hasMany(FamilyProductVersion::class)->withoutGlobalScopes();
    }

    public function product_status()
    {
        return $this->hasManyThrough(ChannelProductStatus::class, ChannelProduct::class, 'product_id', 'channel_product_id', 'id', 'id');
    }

    public function Product_version()
    {
        return $this->hasMany(ProductVersion::class);
    }

    public function inventories()
    {
        return $this->hasMany(Inventory::class)->whereNull('variant_id');
    }

    public function channel()
    {
        return $this->hasOne(ChannelProduct::class, 'product_id', 'id');
    }




    /**
     * This code refactor is started on 16-02-2023
     * All the functions written above will be obsolete.
     */


    /**
     * Helpers
     */

    public function score(): float
    {
        $scores = DB::table('product_version')
            ->where('product_id', $this->id)
            ->pluck('score')
            ->toArray();

        return empty($scores) ? 0 : array_sum($scores) / count($scores);
    }

    public function scoreByVersionId($version_id): float
    {
        return DB::table('product_version')
            ->where('version_id', $version_id)
            ->where('product_id', $this->id)
            ->value('score');
    }


    /**
     * Relationships
     */

    /**
     * Return all the related brands of product (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function brands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class)->withTimestamps()->withoutGlobalScopes();
    }

    /**
     * Return all the related categories of product (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class)->withTimestamps()->withoutGlobalScopes();
    }

    /**
     * Return all the related invites of the vendor or retailer (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function invites(): BelongsToMany
    {
        return $this->belongsToMany(Invite::class)->withTimestamps()->withoutGlobalScopes();
    }

    /**
     * Return all the related brands of product (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function channels(): BelongsToMany
    {
        return $this->belongsToMany(Channel::class)->withPivot('id')->withTimestamps()->withoutGlobalScopes();
    }

    /**
     * Return related organization of product (one-to-many relationship).
     *
     * @return BelongsTo
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Return all the related versions of product (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function versions(): BelongsToMany
    {
        return $this->belongsToMany(Version::class)->withPivot("id AS pivotId")->withoutGlobalScopes();
    }

    /**
     * Return all the related files of the product (many-to-many relationship).
     *
     * @return BelongsToMany
     */
    public function files(): BelongsToMany
    {
        return $this->belongsToMany(File::class)->withPivot('uploaded_for', 'created_at', 'updated_at')->withTimestamps()->withoutGlobalScopes();
    }

    public function file()
    {
        //        (File::class)->withPivot('uploaded_for','created_at','updated_at')->withTimestamps()->withoutGlobalScopes();
        return $this->hasOneThrough(File::class, FileProduct::class, 'product_id', 'id', 'id', 'file_id')->withoutGlobalScopes();
    }

    /**
     * Return all the related variants of the product (one-to-many relationship).
     *
     * @return HasMany
     */
    public function variants(): HasMany
    {
        return $this->hasMany(Variant::class)->withoutGlobalScopes();
    }

    public function ChannelVariants()
    {
        return $this->hasManyThrough(ChannelVariant::class, Variant::class, 'product_id', 'variant_id', 'id', 'id');
    }

    /**
     * Return all the attribute values of the related product (one-to-many relationship).
     *
     * @return HasMany
     */
    public function values(): HasMany
    {
        return $this->hasMany(AttributeFamilyProductVersion::class);
    }


    public function settings()
    {
        return $this->morphOne(ProductVariantSetting::class, 'morphable');
    }


    public function productChannels()
    {
        return $this->hasMany(ChannelProduct::class, 'product_id', 'id');
    }
}
