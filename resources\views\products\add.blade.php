<?php ?>
@extends('layouts.app_new')
@if (request()->has('id'))
    @section('titles', 'Edit Products')
@else
    @section('titles', 'Add Product')
@endif
@section('content')
    @push('header_scripts')
        <link href="{{ asset('css/tagger.css') }}" rel="stylesheet">
        <style>
            .formStyle .tox-tinymce {
                height: 300px !important;
            }
        </style>
    @endpush
    <div>
        <x-products.edit-product-base-form :product="$product" :version="$current_version">
            <x-products.edit-product-header :product="$product" :version="$current_version" />
            <x-products.edit-product-header-navs :product="$product" :version="$current_version" />
            {{-- old code start --}}
            <div class="row">
                <div class="col-12 col-xl-8" id="product_content">
                    <div class="tab-content" id="pills-tabContent">
                        {{-- GENERAL --}}

                        @foreach ($default_families as $key => $family)
                            @if (strtolower($family->name) === 'general')
                                <div class="tab-pane" id="{{ strtolower($family->name) }}-tab" role="tabpanel"
                                    aria-labelledby="pills-general-tab">
                                    @if (!$key)
                                        {{-- switch html --}}
                                        <div class="d-flex align-items-center mb-3 mb-lg-2 mb-xl-3">
                                            <div class="formStyle">
                                                <label>
                                                    {{ trans('products_edit.status') }}
                                                </label>
                                            </div>
                                            <div class="custom-control custom-switch ms-3 formStyle">
                                                <label for="customSwitch1" class="me-2">
                                                    {{ trans('products_edit.draft') }}
                                                </label>
                                                <div class="form-check form-switch d-inline" id="draft">
                                                    <input name="status" type="checkbox" value="1"
                                                        class="form-check-input" data-confirm-before-leave="true"
                                                        id="customSwitch1" {{ $product->status ? 'checked' : '' }}>
                                                    <label class="ms-2" for="customSwitch1">
                                                        {{ trans('products_edit.published') }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        {{-- input html --}}
                                        <div class="">
                                            <div class="formStyle mb-3 mb-lg-2 mb-xl-3">
                                                <label for="sku" class="d-flex align-items-end">
                                                    {{ trans('products_edit.handle') }}
                                                    &nbsp;<span class="text-danger">*</span>
                                                    @php($id = $product->id . '_product')
                                                    <x-products.edit-product-attribute-status-dot :id="$id" />
                                                </label>
                                                <input type="text"
                                                    class="form-control @error('sku') is-invalid @enderror"
                                                    data-confirm-before-leave="true" id="sku" name="sku"
                                                    data-rules='{"required":1,"max":255}'
                                                    data-status-id="#dot_{{ $id }}" value="{{ $product->sku }}"
                                                    autofocus required>
                                                @error('sku')
                                                    <span class="text-danger" role="alert">
                                                        <small>{{ $message }}</small>
                                                    </span>
                                                @enderror
                                            </div>
                                        </div>
                                    @endif

                                    @foreach ($family->attributes as $attribute)
                                        @if ($attribute->handle == 'description')
                                            @if ($variants->count() == 1)
                                                <div id="novariants" class="formStyle mb-3 mb-lg-2 mb-xl-3"></div>
                                                @vite('resources/js/components/productVariants/novariants.jsx')
                                            @endif
                                        @endif
                                        {{-- Quantity field label remove --}}

                                        <div class="formStyle mb-3 mb-lg-2 mb-xl-3">
                                            @if ($attribute->attribute_type->id == 2 && json_decode($attribute->rules, true)['type'] == 'integer')
                                            @else
                                                <label for=""> {{ $attribute->name ?? '' }}&nbsp;
                                                    <x-products.edit-product-attribute-status-dot :id="$attribute->pivotId" />
                                                </label>
                                            @endif
                                            @if ($attribute->attribute_type->id == 2 && json_decode($attribute->rules, true)['type'] == 'price')
                                                <div class="input-group mb-3 mr-sm-2">
                                                    <div class="input-group-prepend">
                                                        <span
                                                            class="form-control h-36 fw-400 d-flex align-items-center custom-border-css fs-14">
                                                            {{ $currency->currency }} </span>
                                                    </div>
                                                    <input type="text"
                                                        name="attribute[{{ $attribute->pivotId }}][value]"
                                                        data-confirm-before-leave="true"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                                        value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                        data-rules="{{ $attribute->text_validation() }}"
                                                        data-status-id="#dot_{{ $attribute->pivotId }}"
                                                        class="form-control" placeholder="" id="{{ $attribute->handle }}">
                                                </div>
                                            @elseif($attribute->attribute_type->id == 2 && json_decode($attribute->rules, true)['type'] == 'integer')
                                                {{-- qunatity field --}}
                                                {{-- <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                            data-confirm-before-leave="true"
                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                            value="{{ isset($attribute->value) ?  $attribute->value->pluck('value')->first() : '' }}"
                            data-rules="{{ $attribute->text_validation() }}"
                            data-status-id="#dot_{{ $attribute->pivotId }}"
                            @if (isset(json_decode($attribute->rules, true)['type']) && json_decode($attribute->rules, true)['type'] == 'integer')
                            onkeypress="return (event.charCode == 8 || event.charCode == 0) ? null :
                            event.charCode >= 48 && event.charCode <= 57" @endif class="form-control" placeholder="" id="{{$attribute->handle}}"> --}}

                                            @elseif($attribute->attribute_type->id == 3)
                                                <div class="position-relative ">

                                                    <div id="myTextarea1">
                                                        <img src={{ asset('assets/images/tinymce-header.png') }}
                                                            style="max-width:100%;min-width:100%;width:100%;"
                                                            class="border">

                                                        <div id="formattedContent" class="border p-3"
                                                            style="width:100%;height:190px;cursor:text;overflow-y:auto;">
                                                            {!! isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' !!}
                                                        </div>

                                                    </div>
                                                    <textarea id="myTextareacontainer" cols="20" rows="8" placeholder="Describe here..." style="opacity:0;"
                                                        name="attribute[{{ $attribute->pivotId }}]" data-confirm-before-leave="true"
                                                        class="form-control  {{ $attribute->handle }}" data-rules="{{ $attribute->text_validation() }}"
                                                        data-status-id="#dot_{{ $attribute->pivotId }}">{!! isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' !!}
                                    </textarea>
                                                </div>
                                            @elseif($attribute->attribute_type->id == 4)
                                                <select name="attribute[{{ $attribute->pivotId }}]" value=""
                                                    data-confirm-before-leave="true"
                                                    data-rules="{{ $attribute->text_validation() }}"
                                                    data-status-id="#dot_{{ $attribute->pivotId }}" class="form-control"
                                                    autofocus>
                                                    @foreach ($attribute->attribute_options as $option)
                                                        <option value="{{ $option->id }}">{{ $option->name }}</option>
                                                    @endforeach
                                                </select>
                                            @elseif($attribute->attribute_type->id == 7)
                                                <div class="input-group mb-3 mr-sm-2">
                                                    <div class="input-group-prepend">
                                                        <select name="attribute[{{ $attribute->pivotId }}][measurement]"
                                                            data-confirm-before-leave="true"
                                                            class="form-control input-prepend-select pr-2 check-box-product ">
                                                            <option value="oz"
                                                                {{ isset($attribute->value) ? ($attribute->value->pluck('unit')->first() == 'oz' ? 'selected' : '') : ($organization_settings['units'] == 'oz' ? 'selected' : '') }}>
                                                                {{ __('oz') }}
                                                            </option>
                                                            <option value="lb"
                                                                {{ isset($attribute->value) ? ($attribute->value->pluck('unit')->first() == 'lb' ? 'selected' : '') : ($organization_settings['units'] == 'lb' ? 'selected' : '') }}>
                                                                {{ __('lb') }}
                                                            </option>
                                                            <option value="kg"
                                                                {{ isset($attribute->value) ? ($attribute->value->pluck('unit')->first() == 'kg' ? 'selected' : '') : ($organization_settings['units'] == 'kg' ? 'selected' : '') }}>
                                                                {{ __('kg') }}
                                                            </option>
                                                            <option value="g"
                                                                {{ isset($attribute->value) ? ($attribute->value->pluck('unit')->first() == 'g' ? 'selected' : '') : ($organization_settings['units'] == 'g' ? 'selected' : '') }}>
                                                                {{ __('g') }}
                                                            </option>
                                                        </select>
                                                    </div>
                                                    <input type="text"
                                                        name="attribute[{{ $attribute->pivotId }}][value]"
                                                        value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                        data-confirm-before-leave="true"
                                                        oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');"
                                                        data-rules="{{ $attribute->text_validation() }}"
                                                        data-status-id="#dot_{{ $attribute->pivotId }}"
                                                        class="form-control " @if ($attribute->is_required)  @endif
                                                        id="{{ $attribute->handle }}">
                                                </div>
                                            @elseif($attribute->attribute_type->id == 11)
                                                <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                                                    value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                    data-confirm-before-leave="true"
                                                    data-rules="{{ $attribute->validate_single_values() }}"
                                                    data-status-id="#dot_{{ $attribute->pivotId }}" class="form-control "
                                                    @if ($attribute->is_required)  @endif
                                                    id="{{ $attribute->handle }}">
                                            @elseif($attribute->handle == 'seo_keyword')
                                                {{-- Seo tags --}}
                                                <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                                                    value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                    data-confirm-before-leave="true"
                                                    data-rules="{{ $attribute->text_validation() }}"
                                                    data-status-id="#dot_{{ $attribute->pivotId }}"
                                                    id="{{ $attribute->handle }}" class="form-control tags"
                                                    @if ($attribute->is_required)  @endif />
                                            @else
                                                {{-- Attribute type id 1 --}}
                                                <input type="text" name="attribute[{{ $attribute->pivotId }}]"
                                                    value="{{ isset($attribute->value) ? $attribute->value->pluck('value')->first() : '' }}"
                                                    data-confirm-before-leave="true"
                                                    data-rules="{{ $attribute->text_validation() }}"
                                                    data-status-id="#dot_{{ $attribute->pivotId }}" class="form-control "
                                                    @if ($attribute->is_required)  @endif
                                                    id="{{ $attribute->handle }}">

                                            @endif
                                        </div>
                                    @endforeach

                                </div>
                            @endif
                        @endforeach


                    </div>
                </div>
                {{-- Right Side options --}}
                <div class="col-12 col-xl-4" id="right_sidebar">
                    <div class="row">
                        <div class="col-12 d-none d-xl-block mt-4">
                            {{-- dropdown with search --}}
                            <div class="dropdown float-md-right">
                                <span class="btn-outline dropdown-toggle dropdown-toggle-attribute" type="button"
                                    id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">

                                    {{ $current_version->name }}
                                </span>
                                <div class="dropdown-menu dropdown-menu-attribute" aria-labelledby="dropdownMenuButton">
                                    @foreach ($versions as $version)
                                        <a class="dropdown-item"
                                            href="{{ route('products.edit', ['id' => $product->id, 'version_id' => $version->id]) }}">{{ $version->name }}
                                            @foreach ($product_versions as $product_version)
                                                @if ($product_version->id == $version->id)
                                                    <span class="text-success ms-4"><i class="fas fa-check"></i></span>
                                                @endif
                                            @endforeach
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>

                    <x-completeness-product-fields-scoring :product="$product" :version="$current_version" />
                    <x-products.edit-product-selection-widget :product="$product" />
                    <!-- <x-products.edit-product-footer-btns/> -->

                </div>
            </div>
            {{-- <div class="row"> --}}
            {{-- <div class="col-lg-12">
                   Variants Table Section
                     <div class="formStyle mb-3 mb-lg-2 mb-xl-3">


                            <div id="variants">


                                <div id="product-variants"></div>
                                @vite("resources/js/components/productVariants/index.jsx")


                            </div>
                        </div>
                    </div>
                </div> --}}
            {{-- </div> --}}


        </x-products.edit-product-base-form>
    </div>
@endsection
@push('footer_scripts')
    <script src="{{ asset('js/delete_product.js') }}"></script>
    <script>
        var data = @json($variants);
        //data = data.original;
        window.mappingData = data
        //console.log(bulkeditdata)
        $(document).ready(function() {
            // Listen for the scroll event
            $(window).scroll(function() {
                // Check if the scroll position is more than 0 to determine if it's scrolled down
                if ($(this).scrollTop() > 0) {
                    $(".product-header").addClass("fixed-header shadow");
                    $(".product-header>div").addClass("align-items-center");
                    var width = $('.right-side').width() + 18;
                    $(".product-header").css("width", width);
                } else {
                    $(".product-header").removeClass("fixed-header shadow");
                    $(".product-header").removeAttr("style");
                }
            });
        });
        $("#pills-completeness-tab").on('click', function() {
            document.getElementById('right_sidebar').style.display = 'none';
            $("#product_content").removeClass('col-md-8 col-lg-7 col-xl-8');
        });

        $(".product_data").on('click', function() {
            document.getElementById('right_sidebar').style.display = 'block';
            $("#product_content").addClass('col-md-8 col-lg-7 col-xl-8');
        });


        @if (Session::has('delete'))
            $(document).ready(function() {
                $('#pills-tab a[href="#pills-product-images"]').tab('show');
            })
        @endif

        $("#pills-variation-tab").click(function(e) {
            e.preventDefault();
            $("#pro_edit_form").prepend("<input type='hidden' name='variant_submit' value = '1'>");
            $("#pro_edit_form").submit();
        });

        /**
         * Input status change
         * */



        class Validation {
            /* In constructor we pass Rule and its values */
            constructor(rule, value, rulesAll = null) {
                this.rule = rule;
                this.value = value;
                this.rulesAll = rulesAll;

                this.res = true;
                this.ruleStringToArray();

            }


            /* This method split the values after this symbol "|" */
            ruleStringToArray() {
                let ruleArray = this.rule;
                this.validate(ruleArray);

            }

            // check type of data
            checkType(type) {
                if (this.rulesAll.type) {
                    return this.rulesAll.type === type;
                } else {
                    return this.rulesAll[0] === type;
                }
            }

            /* Rules for validation for all methods */
            validate(ruleArray) {
                const required = /required/g;
                const integer = /min:*/g;
                const decimal = /min:*/g;
                const max = /max:*/g;
                const min = /min:*/g;
                const slug = /slug/g;
                const urlValidator = /(http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
                const regex = /regex/g;
                const precision = /precision/g;
                const dateRegax = /dateRegax/g;
                const before = /before/g;
                const after = /after/g;
                this.messages = {
                    required: "The value is required.",
                    max: "The value should be equal to or smaller than",
                    min: "The value should be equal to or greater than",
                    format: "Please enter the correct format",
                    integer: "Values must be numbers without a decimal",
                    url: "The Url is not valid",
                    regex: "Not valid",
                    precision: "digit after point",
                    after: "The value is not valid for after",
                    before: "The value is not valid for before",
                    character: "Please Enter the character"
                };
                this.errors = [];

                for (let i in ruleArray) {
                    /* This rule is used when filed is mandatory */
                    if (ruleArray[i].match(required)) {
                        if (!this.value) {
                            this.res = false;
                            this.errors.push(this.messages.required);
                        }

                    }
                    /* This rule applies with the length of minimum. */
                    if (ruleArray[i].match(min)) {
                        let matcher = ruleArray;

                        if (($('input[type="text"]')) && !($.isNumeric(this.value)) && (this.value.length < parseFloat(
                                matcher[1]))) {
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }
                        if (this.checkType("date") || this.checkType("datetime-local")) {
                            if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date
                                    .parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                        if ((this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this
                                .checkType("integer") || this.checkType("decimal") || this.checkType("price"))) {
                            if (this.value < matcher[1]) {
                                this.res = false;
                                this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                            }
                        }
                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date
                                .parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                        /* This rule is used to check the upc code */
                    }
                    /* The ruler is used for the maximum character length. */
                    if (ruleArray[i].match(max)) {
                        let matcher = ruleArray;
                        if ((this.checkType("decimal") || this.checkType("integer") || this.checkType("list") || this
                                .checkType("dimension") || this.checkType("weight") || this.checkType("volume") || this
                                .checkType("price")) && (parseFloat(this.value) > parseFloat(matcher[1]))) {
                            if (this.checkType("dimension") || this.checkType("weight") || this.checkType("volume") ||
                                this
                                .checkType("integer") || this.checkType("decimal") || this.checkType("price")) {
                                if (parseFloat(matcher[1]) > 0) {
                                    this.res = false;
                                    this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);
                                }

                            }
                        }
                        if (this.value.length > parseFloat(matcher[1])) {
                            this.res = false;
                            this.errors.push(`${this.messages.max} ${parseFloat(matcher[1])}`);

                        }
                        if (this.checkType("date") || this.checkType("date_and_time")) {
                            if (!((Date.parse(this.value) >= Date.parse(matcher[1])) && (Date.parse(this.value) <= Date
                                    .parse(matcher[2])))) {
                                this.res = false;
                                this.errors.push(this.messages.dateRegax);
                            }
                            this.res = true;
                        }
                        /* This rule is used to check the upc code */
                    }

                    // Rule for integer
                    if (ruleArray[i].match(integer)) {
                        let matcher = ruleArray;
                        if ((parseFloat(this.value) < parseFloat(matcher[1]))) {
                            this.res = false;
                            this.errors.push(`${this.messages.min} ${parseFloat(matcher[1])}`);
                        }

                    }
                    // Rule for decimal number
                    if (ruleArray[i].match(decimal) && this.value.length > 0 || this.checkType("decimal") && this.value
                        .length > 0 || this.checkType("integer") && this.value.length > 0) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value.split("|")[i])) {
                            this.res = false;
                            this.errors.push(`${this.messages.format}`);
                        }

                    }
                    /* It is used to validate URLs.*/
                    if (ruleArray[i].match(slug)) {
                        if (!this.value.match(urlValidator)) {
                            this.res = false;
                            this.errors.push(this.messages.slug);
                        }
                    }
                    /* This ruler is used for checking the regex. */
                    if (ruleArray[i].match(regex)) {
                        let matcher = ruleArray;
                        if (!this.value.match(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.regex);
                        }
                    }
                    /* This rule is used to check the values after point */
                    if (ruleArray[i].match(precision)) {
                        let matcher = ruleArray;

                        if ((this.value + "").split(".")[1].length > parseInt(matcher[1])) {
                            this.res = false;
                            this.errors.push(this.messages.precision);
                        }
                    }
                    /* This rule is used to check the before date */
                    if (ruleArray[i].match(before)) {
                        let matcher = ruleArray;
                        if ((Date.parse(this.value) >= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.before);
                        }
                    }

                    /* This rule is used to check the after date */

                    if (ruleArray[i].match(after)) {
                        let matcher = ruleArray[i].split(":");
                        if ((Date.parse(this.value) <= Date.parse(matcher[1]))) {
                            this.res = false;
                            this.errors.push(this.messages.after);
                        }
                    }
                    /* This rule is used to check the upc_bar code */
                    if (this.checkType("upc_barcode") && this.value.length > 0) {
                        let matcher = ruleArray;
                        if (!$.isNumeric(this.value)) {
                            this.res = false;
                            this.errors.push(this.messages.format);
                        }
                    }
                    /* This rule is used to check the integer value */
                    if (ruleArray[i].match(integer) && this.checkType("integer") || this.checkType("integer")) {
                        if (this.value.indexOf(".") !== -1) {
                            this.res = false;
                            this.errors.push(this.messages.integer);
                        }
                    }
                    return false;

                }
            }

            /* This method is used to show the result true or false */
            result() {
                return this.res;
            }

            errorMessages() {
                return this.errors;
            }
        }

        /* Validation("Rule:value", "value") */

        function input_status(obj) {
            if ($(obj).attr('data-rules')) {
                let rules_array = $(obj).attr('data-rules');
                let data_id = $(obj).attr('data-status-id');
                var data_rule = JSON.parse(rules_array);
                const keys = Object.keys(data_rule);
                let self = $(obj);
                let flag = true; // true = success, false = error
                keys.forEach((key, index) => {
                    const item = [];
                    if (data_rule[key] == "0") {
                        item[0] = "";
                        item[1] = "";
                    } else {
                        item[0] = key;
                        item[1] = data_rule[key];
                    }
                    //  const item=key+":"+data_rule[key];
                    let validation = new Validation(item, self.val(), data_rule);
                    if (!validation.result() || self.val() < 0) {
                        let chars = validation.errorMessages();
                        let unique_data = [...new Set(chars)];
                        $(data_id).attr("data-bs-content", unique_data)
                        flag = false;
                        return 0;
                    }
                });
                if (flag) {
                    $(data_id).attr("data-bs-content", '✔');
                    $(data_id).removeClass("circle-error", 1000);
                    $(data_id).addClass("circle-success", 1000);


                } else {
                    $(data_id).removeClass("circle-success", 1000);
                    $(data_id).addClass("circle-error", 1000);
                }
            }
        }

        /**
         * Trigger input status change on keyup
         * */
        $('input, select, textarea').keyup(function() {
            input_status(this)
        });
        $('select').change(function() {
            input_status(this)
        });
        /**
         * Trigger input status change on page refresh | page load
         * */
        $(document).ready(function() {
            $("input, select, textarea").each(function() {
                input_status(this)
            });
        });
        $(document).ready(function() {
            $('input[type=date]').change(function() {
                input_status1(this)
            });
        });

        $(document).ready(function() {
            $('input[type=datetime-local]').change(function() {
                input_status1(this)
            });
        });
        $(document).ready(function() {
            $('#justAnotherInputBox').change(function() {
                input_status(this)
            });
        });
        /**
         * Popover trigger
         * */
        const productEl = document.querySelectorAll('.circle-sm');
        productEl.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });
        const productEl1 = document.querySelectorAll('.score-label');
        productEl1.forEach(function(element) {
            var popover = new bootstrap.Popover(element, {
                content: function() {
                    return "This is the dynamic content";
                }
            });
        });
    </script>

    <script>
        function loadTinyMce() {
            var script = document.createElement('script');
            script.type = 'text/javascript';
            script.src =
                'https://cdn.tiny.cloud/1/oh0o94omie4zyuas0tk96qk319s6ltqf8eq9er20jhn3d7r7/tinymce/6/tinymce.min.js';

            document.head.appendChild(script);
            script.onload = function() {
                if (typeof loadedFunction === 'function') {
                    loadedFunction();
                }
                let isDirty = false;
                tinymce.init({
                    selector: '#myTextareacontainer',
                    verify_html: false,
                    plugins: [
                        'a11ychecker', 'advcode', 'advlist', 'anchor', 'autolink', 'codesample',
                        'fullscreen', 'help', 'image', 'editimage', 'tinydrive', 'lists', 'link', 'media',
                        'powerpaste', 'preview', 'searchreplace', 'table', 'template',
                        'tinymcespellchecker', 'visualblocks', 'wordcount'
                    ],
                    toolbar: ' undo redo | bold italic | forecolor backcolor | codesample | alignleft aligncenter alignright alignjustify | bullist numlist ',
                    valid_elements: '*[*]',
                    setup: function(editor) {
                        editor.on('keyup', function(event) {
                            isDirty = true;
                            var content = editor.getContent({
                                format: 'text'
                            });
                            const labelStatus = document.querySelector('#myTextarea1').closest(
                                '.formStyle').querySelector('span');
                            if (content.trim().length <= 0) {
                                labelStatus.classList.replace('circle-success',
                                'circle-error'); // labelStatus.setAttribute('data-bs-content', ' The value is required.' ); // }else if(content.trim().length> 63000){
                                labelStatus.classList.replace('circle-success', 'circle-error');
                                labelStatus.setAttribute('data-bs-content',
                                    'The value should be equal to or smaller than 63000.');
                            } else {
                                labelStatus.classList.replace('circle-error', 'circle-success');
                                labelStatus.setAttribute('data-bs-content', '✔');
                            }
                        });
                    }
                });




            };
            script.onerror = function() {
                console.error('Failed to load script');
            };
        }

        $(document).on('click', '#myTextarea1', function() {
            loadTinyMce();
            $('#myTextarea1').hide();
            $('#myTextareacontainer').css('opacity', '1')
        .show(); // Show the textarea, which will be transformed into the TinyMCE editor
        });

        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('myTextareacontainer');
            const formattedContent = document.getElementById('formattedContent');

            if (textarea && formattedContent) {
                // Populate the formattedContent div with the textarea content
                formattedContent.innerHTML = textarea.value;

                // Apply styles to prevent content overflow
                const contentElements = formattedContent.querySelectorAll('img, table, ul, ol');
                contentElements.forEach(function(element) {
                    if (element.tagName.toLowerCase() === 'img') {
                        // Handle image overflow
                        element.style.maxWidth = '100%';
                        element.style.height = 'auto';
                        element.style.display = 'block';
                    } else if (element.tagName.toLowerCase() === 'table') {
                        // Handle table overflow
                        element.style.width = '100%';
                        element.style.tableLayout = 'fixed';
                        element.style.overflowX = 'auto';
                    } else if (element.tagName.toLowerCase() === 'ul' || element.tagName.toLowerCase() ===
                        'ol') {
                        // Handle list overflow
                        element.style.marginLeft = '20px';
                        element.style.listStylePosition = 'inside';
                    }
                });
                // Hide the textarea and show the formatted content div
                textarea.style.display = 'none';
                formattedContent.style.display = 'block';
            }
        });


        window.addEventListener('beforeunload', function(e) {
            if (isDirty) {
                var confirmationMessage = 'It looks like you have been editing something. ' +
                    'If you leave before saving, your changes will be lost.';

                (e || window.event).returnValue = confirmationMessage; // Gecko + IE
                return confirmationMessage; // Webkit, Safari, Chrome
            }
        });
        document.querySelectorAll('.nav-link').forEach(function(button) {

            button.addEventListener('click', function(event) {

                if (isDirty) {
                    var confirmLeave = confirm('Changes you made may not be saved.');
                    if (!confirmLeave) {
                        event.preventDefault(); // Prevent the navigation if the user cancels
                    } else {
                        isDirty = false; // Reset the dirty flag if the user decides to leave
                    }
                }
            });
        });

        document.getElementById('pro_edit_pub_btn').addEventListener('click', function() {
            isDirty = false;
            window.isSaving = true;
            // proceed with saving logic
        });
    </script>

    <script>
        function showAttributes(data) {
            console.log(data ? data : '');
        }
        @if (isset($product->versions->families))
            @foreach ($product->versions->families as $selected_family)
                @if ($selected_family)
                    $(document).ready(function() {
                        let obj = [];
                        var data = $('#Family').val();
                        obj = $('#Family option:selected').map(function() {
                            return $(this).text();
                        });
                        createTags(obj, data, 'div', 'Family');
                        showAttributes(data);
                    });
                @endif
            @endforeach
        @endif
        //family
        $('select#Family').on('sumo:closed', function(sumo) {
            let obj = [];
            var data = $('#Family').val();
            obj = $('#Family option:selected').map(function() {
                return $(this).text();
            });
            createTags(obj, data, 'div', 'Family');
            showAttributes(data);

        });

        // Brands

        $('select#Brands').on('sumo:closed', function(sumo) {
            let obj = [];
            var data = $('#Brands').val();
            obj = $('#Brands option:selected').map(function() {

                return $(this).text();
            });
            createTags(obj, data, 'div1', 'Brands');
        });

        // Vendors

        $('select#Vendor').on('sumo:closed', function(sumo) {
            let obj = [];
            var data = $('#Vendor').val();
            obj = $('#Vendor option:selected').map(function() {
                return $(this).text();
            });
            createTags(obj, data, 'div2', 'Vendors');
        });

        // Categories

        $('select#category').on('sumo:closed', function(sumo) {
            let obj = [];
            var data = $('#category').val();
            obj = $('#category option:selected').map(function() {
                return $(this).text();
            });
            createTags(obj, data, 'div3', 'category');
        });

        //    $( "#other" ).click(function() {

        function createTags(text, val, id, sumo_id) {
            $('#' + id).html('');
            for (let i = 0; i < text.length; i++) {

                let div = document.getElementById(id);
                // console.log(id);
                var params = val[i] + ",'" + id + "' , '" + sumo_id + "'";
                var func = 'onclick="deleteTab(' + params + ')"';
                $(div).append("<div class='col-5 col-sm-4 col-md-5 col-lg-5 col-xl-4 mt-2 pr-0'><div class='delete_" + id +
                    "_" + val[i] + "'><div class='d-flex flex-row'>" +
                    "<div class='badge py-2 tg text-break text-left black Roboto regular tags ' style='border-radius:  0.25rem 0 0 0.25rem ;'><span>" +
                    text[i] +
                    "</span></div>" +
                    "<div class='float-right mr-2 px-2 py-1' style='background-color: #C9C9CF;border-radius: 0 0.25rem 0.25rem 0;' " +
                    func + ">" +
                    "<img src='{{ asset('media/sidebar/x.png') }}'  width='10' height='10' ></div></div></div></div>");

            }
        }
    </script>



    <script src="{{ asset('js/text_editor.js') }}"></script>

    <script>
        var units = @json($units);
        window.mappingData = units;
    </script>
@endpush
