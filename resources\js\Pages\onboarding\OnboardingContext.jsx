import React, { createContext, useState, useEffect } from "react";

// Create the context
export const OnboardingContext = createContext();

// Create the provider component
export const OnboardingProvider = ({ children }) => {
    // Initialize currentStepIndex from localStorage or default to 0
    const [currentStepIndex, setCurrentStepIndex] = useState(() => {
        if (typeof window !== 'undefined') {
            const savedStep = localStorage.getItem('onboarding_current_step');
            return savedStep ? parseInt(savedStep, 10) : 0;
        }
        return 0;
    });

    const totalSteps = 7; // Update this based on the total number of steps

    // Initialize form data from localStorage or as empty object
    const [formData, setFormData] = useState(() => {
        if (typeof window !== 'undefined') {
            const savedFormData = localStorage.getItem('onboarding_form_data');
            return savedFormData ? JSON.parse(savedFormData) : {};
        }
        return {};
    });

    // Initialize shopifyurl from localStorage or as empty string
    const [shopifyurl, setShopifyurl] = useState(() => {
        if (typeof window !== 'undefined') {
            const savedShopifyUrl = localStorage.getItem('onboarding_shopify_url');
            return savedShopifyUrl || "";
        }
        return "";
    });

    // Effect to persist currentStepIndex to localStorage whenever it changes
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem('onboarding_current_step', currentStepIndex.toString());
        }
    }, [currentStepIndex]);

    // Effect to persist formData to localStorage whenever it changes
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem('onboarding_form_data', JSON.stringify(formData));
        }
    }, [formData]);

    // Effect to persist shopifyurl to localStorage whenever it changes
    useEffect(() => {
        if (typeof window !== 'undefined') {
            localStorage.setItem('onboarding_shopify_url', shopifyurl);
        }
    }, [shopifyurl]);
const currentPath = window.location.pathname;
    // Effect to clear localStorage when user reaches the last step or navigates to /syncproducts
    useEffect(() => {
        console.log("Current step:", currentStepIndex,currentPath );  // Log current step

        if (typeof window !== 'undefined') {
            

            // Check if the path contains "/syncproducts" and if we're at the last step
            if (currentPath.includes("/syncproducts") ) {
                console.log("Completed all steps. Clearing localStorage...");

                // Remove data from localStorage when step 7 (or last step) is completed
                localStorage.removeItem('onboarding_current_step');
                localStorage.removeItem('onboarding_form_data');
                localStorage.removeItem('onboarding_shopify_url');
            } else if (currentStepIndex < totalSteps) {
                // Persist the current step to localStorage if not completed
                localStorage.setItem('onboarding_current_step', currentStepIndex.toString());
            }
        }
    }, [currentStepIndex, totalSteps, currentPath]);

    const handleNext = () => {
        setCurrentStepIndex((prev) => Math.min(prev + 1, totalSteps));
    };

    const handleBack = () => {
        setCurrentStepIndex((prev) => Math.max(prev - 1, 0));
    };

    // Function to update form data for a specific step
    const updateFormData = (stepKey, data) => {
        setFormData((prev) => ({
            ...prev,
            [stepKey]: data,
        }));
    };
    console.log("form data", shopifyurl, currentStepIndex);

    const value = {
        currentStepIndex,
        handleNext,
        handleBack,
        totalSteps,
        formData,
        updateFormData,
        shopifyurl,
        setShopifyurl,
    };

    return <OnboardingContext.Provider value={value}>{children}</OnboardingContext.Provider>;
};
