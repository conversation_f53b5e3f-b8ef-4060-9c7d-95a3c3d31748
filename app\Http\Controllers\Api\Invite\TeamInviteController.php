<?php

namespace App\Http\Controllers\Api\Invite;

use App\Http\Controllers\Controller;
use App\Http\Requests\Invite\inviteRequest;
use App\Http\Requests\Invite\invitesRequest;

use App\Jobs\ProcessEmailInvite;
use App\Models\Organization\Organization;
use Illuminate\Http\Request;

class TeamInviteController extends Controller
{
    public $inviteService = null;

    public function __construct()
    {
        $this->middleware(['auth:sanctum']);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        if (get_class($user) != 'App\Models\Organization\Organization') {
            $this->check_user_organization($user, $request);
        }

        $teaminvites = $user->teamInvites();
        // Determine the number of items per page
        $paginate = $request->filled("paginate") ? $request->get("paginate") : 255;

        // Get the paginated data
        $teaminvites = $teaminvites->orderBy('updated_at', 'desc')->paginate($paginate);

        // Return the response with pagination details
        return response(
            [
                'message' => 'Invites retrieved successfully',
                'invites' => $teaminvites
            ]
        );
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(InvitesRequest $request)
    {
        if (get_class($request->user()) != Organization::class) {
            $this->check_user_organization($request->user(), $request);
        }
        $user = $request->user();
        foreach ($request->input('emails') as $email) {
            $teamInvite = $user->teamInvites()->create(['email' => $email]);
            //set all permissions
            $teamInvite->permissions()->sync([1, 2, 3, 4, 5]);

            //send
            ProcessEmailInvite::dispatch($teamInvite, $user);
        }

        return response(
            [
                'message' => 'Invites created successfully',
            ]
        );
    }



    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $teamInvite = $request->user()->teamInvites()->findOrFail($id);
        return response(
            [
                'message' => 'Invite retrieved successfully',
                'invite' => $teamInvite
            ]
        );
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Request $request, string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(inviteRequest $request, string $id)
    {
        $teamInvite = $request->user()->teamInvites()->findOrFail($id);
        $teamInvite->update($request->all());
        //set all permissions
        $teamInvite->permissions()->sync([1, 2, 3, 4, 5]);

        //send
        ProcessEmailInvite::dispatch($teamInvite, $request->user());
        return response(
            [
                'message' => 'Invite updated successfully',
                'invite' => $teamInvite
            ]
        );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id)
    {
        $teamInvite = $request->user()->teamInvites()->findOrFail($id);
        $teamInvite->delete();
        return response(
            [
                'message' => 'Invite deleted successfully'
            ]
        );
    }
}
