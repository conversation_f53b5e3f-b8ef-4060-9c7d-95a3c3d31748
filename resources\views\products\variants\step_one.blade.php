<?php ?>
@extends('layouts.app_new')
@if(request()->has('id'))
    @section('titles','Edit Products')
@else
    @section('titles','Update Variants')
@endif
@section('content')

    <!--header data-->
    <x-products.edit-product-header :product="$product" :buttons="false" :version="$current_version"/>
    <x-products.edit-product-header-navs :product="$product" :version="$current_version"/>


    {{--  step 1 new code start  --}}
    <div class="row">
        <div class="col-12 d-flex justify-content-end">
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#exampleModal" onclick="preventDefault()">
                {{trans('products_variants_step1.add_attributes_btn')}}
            </button>
        </div>
        <form id="var_step1_form" method="get" action="{{route('variants.step.two')}}">
            @csrf
            <input type="hidden" value="{{$id}}" name="product_id">
            <input type="hidden" value="{{$current_version->id}}" name="version_id">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">{{trans('products_variants_step1.step1_title')}}</h3>
                </div>
                {{--  table start   --}}
                <table class="table mt-3">
                    <thead>
                    <tr>
                        <th>{{ __('VARIANT OPTION NAME') }}</th>
                        <th scope="col">Type</th>
                        <th scope="col">Group</th>
                        <th scope="col">By Default</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse($attributes as $attribute)
                        <tr>
                            <td>
                                <input type="checkbox" id="variant-{{$attribute->id}}" name="attribute_id[]" value="{{$attribute->id}}" {{isset($selected_variants_id) ? in_array($attribute->id, $selected_variants_id) ? "checked": '' : ''}}
                                style = "{{isset($selected_variants_id) ? in_array($attribute->id, $selected_variants_id) ? "pointer-events: none; opacity:0.7 " : '' : ''}}" class="form-check-input mt-0">&nbsp;
                                {{$attribute->name}}
                            </td>
                            <td>{{__('Multiselect')}}</td>
                            <td>N/A</td>
                            <td>{{$attribute->is_default == 1 ? 'Yes' : 'No'}}</td>
                        </tr>
                    @empty
                    @endforelse
                    </tbody>
                </table>
                {{-- table end   --}}
                @if(sizeof($attributes) == 0)
                    <tr>
                        <p class="Roboto text-center">{{trans('products_variants_step1.empty_table_description')}}</p>
                    </tr>
                @endif
            </div>
            @if(sizeof($attributes) > 0)
                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" id="next-btn" class="btn btn-primary">
                        {{trans('products_variants_step1.next_btn')}}
                    </button>
                </div>
            @endif
        </form>
    </div>
    {{--  step 1 new code end  --}}

    <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">

        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="{{route('attributes.store')}}" method="post">
                    @csrf
                    <div class="modal-header">
                        <h3 class="modal-title"
                            id="exampleModalLabel">{{trans('products_variants_step1.model_title')}}</h3>
                        <button type="button" class="btn-sm border-0 bg-white close fs-24" data-bs-dismiss="modal"
                                aria-label="Close">

                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="attribute_type_id" value="13">
                        {{--Attribute Name--}}
                        <div class="formStyle pt-0">
                            <label for="name" class="mb-0">{{trans('products_variants_step1.attribute_title')}}
                                &nbsp;<span
                                    class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror"
                                   id="name" name="name"
                                   value="{{--{{ old('name', isset($attribute) ? $attribute->name : null) }}--}}"
                                   required
                                   autofocus>
                            @error('name')
                            <span class="text-danger">
                                        <small>{{$message}}</small>
                                    </span>
                            @enderror
                        </div>

                        {{--   new code start--}}
                        <div class="form-group mt-3">
                            <label for="">{{trans('variant_attribute_update.manage_option')}}</label>
                            <div class="dynamic-field formStyle">
                                @if(old("attribute_options"))
                                    @foreach(old("attribute_options") as $key => $attribute_option)
                                        <div class="d-flex justify-content-between dynamic_fields_js mt-3">
                                            <input type="text" value="{{$attribute_option["name"]}}"
                                                   name="attribute_options[][name]"
                                                   class="form-control form-control-sm duplicate-validation variant-option @error('attribute_options.'.$key.'.name') is-invalid @enderror">
                                            <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">
                                                <img src="http://localhost:8000/media/retailer-dashboard/delete.png"
                                                     alt="">
                                            </button>
                                        </div>
                                        @error('attribute_options.'.$key.'.name')
                                        <span class="text-danger">
                                                <small>{{$message}}</small>
                                            </span>
                                        @enderror

                                    @endforeach
                                    {{--                                @elseif(isset($attribute))--}}
                                    {{--                                    @foreach($attribute->attribute_options as $attribute_option)--}}
                                    {{--                                        <div class="d-flex justify-content-between dynamic_fields_js mt-3">--}}
                                    {{--                                            <input type="text" value="{{$attribute_option->name}}"--}}
                                    {{--                                                   name="attribute_options[][name]"--}}
                                    {{--                                                   class="form-control form-control-sm duplicate-validation variant-option">--}}
                                    {{--                                            <button class="border-0 bg-white ms-2" onclick="deleteRow(this)">--}}
                                    {{--                                                <img src="http://localhost:8000/media/retailer-dashboard/delete.png"--}}
                                    {{--                                                     alt="">--}}
                                    {{--                                            </button>--}}
                                    {{--                                        </div>--}}
                                    {{--                                    @endforeach--}}
                                @endif
                            </div>
                            <button type="button" id="add-btn"
                                    class="btn btn-sm btn-primary mb-2 mt-2" onclick="addRow()">
                                {{trans('variant_attribute_update.add_option_btn')}}

                            </button>
                            @error('attribute_options')
                            <span class="text-danger">
                                <small>{{$message}}</small>
                            </span>
                            @enderror
                        </div>
                        {{--  new code end --}}

                    </div>
                    <div class="modal-footer py-3 px-3">
                        <button type="button" class="btn btn-light border" data-bs-dismiss="modal">Close</button>

                        <button type="submit" class="btn btn-primary">Save changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection
@push('footer_scripts')

    <script>
        $(document).ready(function() {
            @error('name')
            $('#exampleModal').modal('show');
            @enderror

            @error('family')
            $('#exampleModal').modal('show');
            @enderror

            @error('attribute_options')
            $('#exampleModal').modal('show');
            @enderror
        })
    </script>
    {{--!  add variant option script !--}}
    <script src="{{ asset('assets/js/add-attribute-options.js') }}"></script>
@endpush


